<?php
/**
 * Test Password Change Fix
 * Simple test to verify the password change functionality works
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Password Change Fix Test</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="password"], input[type="text"] { 
        width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
    }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
    .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
    .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
</style>';

// Handle test user creation
if (isset($_POST['create_test_user'])) {
    echo '<div class="step">';
    echo '<h2>Creating Test User</h2>';
    
    $test_username = 'pwtest_' . time();
    $test_email = 'pwtest_' . time() . '@example.com';
    $test_password = 'TestPass123';
    
    $user_id = wp_create_user($test_username, $test_password, $test_email);
    
    if (is_wp_error($user_id)) {
        echo "<p class='error'>Failed to create test user: " . $user_id->get_error_message() . "</p>";
    } else {
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        echo "<p class='success'>✅ Test user created and logged in</p>";
        echo "<p><strong>Username:</strong> {$test_username}</p>";
        echo "<p><strong>Password:</strong> {$test_password}</p>";
        echo "<p><strong>User ID:</strong> {$user_id}</p>";
        echo "<p class='info'>You can now test the password change functionality.</p>";
    }
    echo '</div>';
}

// Handle password change test using the exact dashboard form structure
if (isset($_POST['ln_reader_password_change_nonce']) && wp_verify_nonce($_POST['ln_reader_password_change_nonce'], 'ln_reader_password_change')) {
    echo '<div class="alert alert-info">';
    echo '<h3>Password Change Form Submitted</h3>';
    echo '<p>The form was submitted with the correct nonce. The password change function should process this.</p>';
    echo '<p><strong>Check the results below and in your error log.</strong></p>';
    echo '</div>';
}

// Check for success/error messages
if (isset($_GET['password_changed']) && $_GET['password_changed'] == '1') {
    echo '<div class="alert alert-success">';
    echo '<h3>✅ Password Changed Successfully!</h3>';
    echo '<p>The password change was completed successfully. You should now be able to log in with your new password.</p>';
    echo '</div>';
}

// Check for session errors
if (!session_id()) session_start();
if (isset($_SESSION['password_change_errors'])) {
    echo '<div class="alert alert-danger">';
    echo '<h3>❌ Password Change Errors:</h3>';
    echo '<ul>';
    foreach ($_SESSION['password_change_errors'] as $error) {
        echo '<li>' . esc_html($error) . '</li>';
    }
    echo '</ul>';
    echo '</div>';
    unset($_SESSION['password_change_errors']);
}

echo '<div class="step">';
echo '<h2>Current Status</h2>';

if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo "<p><strong>Logged in as:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
    echo "<p><strong>Email:</strong> {$current_user->user_email}</p>";
    echo "<p><strong>Roles:</strong> " . implode(', ', $current_user->roles) . "</p>";
} else {
    echo "<p class='warning'>No user is currently logged in.</p>";
}

// Function status
$function_exists = function_exists('ln_reader_handle_password_change');
echo "<p><strong>Password change function exists:</strong> " . 
     ($function_exists ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . "</p>";

if ($function_exists) {
    $hooked = has_action('init', 'ln_reader_handle_password_change');
    echo "<p><strong>Function hooked to 'init':</strong> " . 
         ($hooked ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . "</p>";
}
echo '</div>';

// Test forms
if (!is_user_logged_in()) {
    echo '<div class="step">';
    echo '<h2>Create Test User</h2>';
    echo '<p>First, create a test user to test the password change functionality:</p>';
    echo '<form method="post">';
    echo '<button type="submit" name="create_test_user" class="btn">Create Test User & Login</button>';
    echo '</form>';
    echo '</div>';
} else {
    echo '<div class="step">';
    echo '<h2>Test Password Change</h2>';
    echo '<p>This form uses the exact same structure as the dashboard password change form:</p>';
    
    echo '<form method="post" action="">';
    wp_nonce_field('ln_reader_password_change', 'ln_reader_password_change_nonce');
    
    echo '<div class="form-group">';
    echo '<label for="current_password">Current Password:</label>';
    echo '<input type="password" id="current_password" name="current_password" placeholder="Enter current password" required>';
    echo '<small>Hint: If you just created a test user, the password is "TestPass123"</small>';
    echo '</div>';
    
    echo '<div class="form-group">';
    echo '<label for="new_password">New Password:</label>';
    echo '<input type="password" id="new_password" name="new_password" placeholder="Enter new password (min 6 chars)" required>';
    echo '</div>';
    
    echo '<div class="form-group">';
    echo '<label for="confirm_password">Confirm New Password:</label>';
    echo '<input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm new password" required>';
    echo '</div>';
    
    echo '<button type="submit" class="btn">Change Password</button>';
    echo '</form>';
    echo '</div>';
    
    echo '<div class="step">';
    echo '<h2>Test Instructions</h2>';
    echo '<ol>';
    echo '<li>Fill out the password change form above</li>';
    echo '<li>Click "Change Password"</li>';
    echo '<li>Check for success/error messages</li>';
    echo '<li>If successful, try logging out and back in with the new password</li>';
    echo '</ol>';
    echo '</div>';
}

echo '<div class="step">';
echo '<h2>Debug Information</h2>';
echo '<p><strong>Current URL:</strong> ' . $_SERVER['REQUEST_URI'] . '</p>';
echo '<p><strong>Request Method:</strong> ' . $_SERVER['REQUEST_METHOD'] . '</p>';
echo '<p><strong>POST Data Present:</strong> ' . (empty($_POST) ? 'No' : 'Yes') . '</p>';

if (!empty($_POST)) {
    echo '<p><strong>POST Keys:</strong> ' . implode(', ', array_keys($_POST)) . '</p>';
}

echo '<p><strong>WordPress Debug:</strong> ' . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . '</p>';
echo '<p><strong>WordPress Debug Log:</strong> ' . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . '</p>';

if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
    $log_file = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($log_file)) {
        echo '<p><strong>Debug Log Location:</strong> ' . $log_file . '</p>';
        echo '<p><em>Check this file for "LN Reader Password Change" entries</em></p>';
    }
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Additional Links</h2>';
echo '<ul>';
echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Visit Actual Dashboard</a></li>';
echo '<li><a href="' . home_url('/dashboard#password') . '" target="_blank">Visit Dashboard Password Tab</a></li>';
echo '<li><a href="debug-password-change-complete.php">Run Complete Debug Tool</a></li>';
echo '<li><a href="' . wp_logout_url() . '">Logout</a></li>';
echo '</ul>';
echo '</div>';

echo '<hr>';
echo '<p><em>Test completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
