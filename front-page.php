<?php get_header(); ?>

<div class="container mt-4">
    <!-- Latest Updates -->
    <section class="latest-updates mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-4">
            <h2 class="h4 mb-0">Latest Updates</h2>
            <a href="<?php echo get_post_type_archive_link('novel'); ?>" class="btn btn-primary btn-sm">View All</a>
        </div>
        
        <div class="row row-cols-1 row-cols-md-2 g-4">
            <!-- Latest Novels Grid -->
            <div class="col-lg-8">
                <div class="row row-cols-1 row-cols-sm-2 row-cols-lg-3 g-4">
                    <?php
                    $latest = new WP_Query([
                        'post_type' => 'novel',
                        'posts_per_page' => 4,
                        'orderby' => 'modified',
                        'order' => 'DESC'
                    ]);
                    if ($latest->have_posts()) : while ($latest->have_posts()) : $latest->the_post();
                    ?>
                    <div class="col">
                        <div class="card h-100 novel-card">
                            <div class="novel-cover">
                                <?php if (has_post_thumbnail()) : ?>
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail('novel-card', ['class' => 'card-img-top']); ?>
                                    </a>
                                <?php endif; ?>
                                <?php 
                                $status = get_post_meta(get_the_ID(), '_novel_status', true);
                                if ($status) : ?>
                                    <div class="novel-status <?php echo strtolower($status); ?>">
                                        <?php echo $status; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-body p-1">
                                <h5 class="card-title h6 mb-2">
                                    <a href="<?php the_permalink(); ?>" class="text-decoration-none text-truncate d-block" title="<?php the_title(); ?>"><?php the_title(); ?></a>
                                </h5>
                                <div class="novel-rating mb-2">
                                    <?php
                                    $rating = get_novel_rating(get_the_ID());
                                    $average = isset($rating['average']) ? floatval($rating['average']) : 0;
                                    $count = isset($rating['count']) ? intval($rating['count']) : 0;
                                    ?>
                                    <div class="d-flex align-items-center gap-1">
                                        <i class="fas fa-star text-warning"></i>
                                        <span class="rating-value"><?php echo number_format($average, 1); ?></span>
                                        <small class="text-muted">(<?php echo $count; ?>)</small>
                                    </div>
                                </div>
                                <div class="novel-meta">
                                    <?php 
                                    // Get last 2 chapters
                                    $chapters = get_posts([
                                        'post_type' => 'post',
                                        'posts_per_page' => 2,
                                        'meta_key' => '_novel_id',
                                        'meta_value' => get_the_ID(),
                                        'orderby' => 'date',
                                        'order' => 'DESC'
                                    ]);

                                    if ($chapters) :
                                        foreach ($chapters as $chapter) :
                                            $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                            $novel_slug = get_post_field('post_name', get_the_ID());
                                            $chapter_url = home_url("/{$novel_slug}/chapter-{$chapter_number}");
                                            $time_diff = human_time_diff(strtotime($chapter->post_date), current_time('timestamp'));
                                            ?>
                                            <div class="chapter-item">
                                                <a href="<?php echo esc_url($chapter_url); ?>" class="btn btn-sm btn-light text-truncate w-100 d-flex justify-content-between align-items-center">
                                                    <span class="chapter-number">Chapter <?php echo $chapter_number; ?></span>
                                                    <span class="chapter-time text-muted"><?php echo $time_diff; ?> ago</span>
                                                </a>
                                            </div>
                                            <?php
                                        endforeach;
                                    endif;
                                    wp_reset_postdata();
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php 
                    endwhile; endif;
                    wp_reset_postdata();
                    ?>
                </div>
            </div>

            <!-- Popular This Week -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-primary text-white py-2">
                        <h3 class="h6 mb-0">Popular This Week</h3>
                    </div>
                    <div class="list-group list-group-flush">
                        <?php
                        $popular = new WP_Query([
                            'post_type' => 'novel',
                            'posts_per_page' => 3,
                            'meta_key' => 'post_views',
                            'orderby' => 'meta_value_num',
                            'order' => 'DESC'
                        ]);
                        if ($popular->have_posts()) : while ($popular->have_posts()) : $popular->the_post();
                        ?>
                        <a href="<?php the_permalink(); ?>" class="list-group-item list-group-item-action py-2">
                            <div class="d-flex align-items-center gap-2">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="flex-shrink-0">
                                        <?php the_post_thumbnail('novel-thumb', ['class' => 'popular-thumb']); ?>
                                    </div>
                                <?php endif; ?>
                                <div class="flex-grow-1 min-width-0">
                                    <h6 class="mb-0 text-truncate" style="max-width: 200px;" title="<?php the_title(); ?>">
                                        <?php the_title(); ?>
                                    </h6>
                                    <div class="small text-muted d-flex align-items-center gap-2">
                                        <span><i class="fas fa-eye"></i> <?php echo get_post_views(get_the_ID()); ?></span>
                                        <?php 
                                        $last_chapter = get_novel_last_chapter(get_the_ID());
                                        if ($last_chapter) {
                                            echo '<span class="text-primary">Ch. ' . get_post_meta($last_chapter->ID, '_chapter_number', true) . '</span>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </a>
                        <?php 
                        endwhile; endif;
                        wp_reset_postdata();
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Browse by Genre -->
    <section class="genres mb-5">
        <div class="section-header d-flex justify-content-between align-items-center mb-3">
            <h2 class="h5 mb-0"><i class="fas fa-tags me-2"></i>Browse by Genre</h2>
        </div>
        <div class="row row-cols-2 row-cols-md-4 row-cols-lg-6 g-2">
            <?php
            // Define genre icons and colors
            $genre_styles = [
                'action' => ['icon' => 'fas fa-fist-raised', 'color' => '#e74c3c'],
                'adventure' => ['icon' => 'fas fa-compass', 'color' => '#3498db'],
                'comedy' => ['icon' => 'fas fa-laugh', 'color' => '#f1c40f'],
                'drama' => ['icon' => 'fas fa-theater-masks', 'color' => '#9b59b6'],
                'fantasy' => ['icon' => 'fas fa-dragon', 'color' => '#2ecc71'],
                'horror' => ['icon' => 'fas fa-ghost', 'color' => '#95a5a6'],
                'mystery' => ['icon' => 'fas fa-search', 'color' => '#34495e'],
                'romance' => ['icon' => 'fas fa-heart', 'color' => '#e84393'],
                'sci-fi' => ['icon' => 'fas fa-rocket', 'color' => '#00cec9'],
                'slice-of-life' => ['icon' => 'fas fa-coffee', 'color' => '#fdcb6e'],
                'sports' => ['icon' => 'fas fa-football-ball', 'color' => '#2980b9'],
                'supernatural' => ['icon' => 'fas fa-magic', 'color' => '#8e44ad']
            ];

            $genres = get_terms([
                'taxonomy' => 'novel_genre',
                'hide_empty' => true,
                'number' => 12
            ]);
            foreach ($genres as $genre) :
                $genre_slug = $genre->slug;
                $style = isset($genre_styles[$genre_slug]) ? $genre_styles[$genre_slug] : ['icon' => 'fas fa-bookmark', 'color' => '#3498db'];
            ?>
            <div class="col">
                <a href="<?php echo get_term_link($genre); ?>" class="genre-card">
                    <div class="card h-100">
                        <div class="card-body p-2 text-center">
                            <div class="genre-icon mb-2">
                                <i class="<?php echo $style['icon']; ?>" style="color: <?php echo $style['color']; ?>"></i>
                            </div>
                            <h6 class="genre-name mb-1"><?php echo $genre->name; ?></h6>
                            <small class="text-muted"><?php echo $genre->count; ?> Novels</small>
                        </div>
                    </div>
                </a>
            </div>
            <?php endforeach; ?>
        </div>
    </section>
</div>

<style>
/* Novel Cards */
.novel-card {
    transition: transform 0.2s;
    border: 1px solid rgba(0,0,0,.125);
    background: #fff;
    width: 100%;
}

.novel-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.novel-cover {
    position: relative;
}

.novel-card .card-img-top {
    height: 320px;
    object-fit: contain;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 8px;
}

.card-body {
    padding: 1rem;
}

.novel-status {
    position: absolute;
    top: 12px;
    right: 12px;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.novel-status.ongoing {
    background-color: #3498db;
}

.novel-status.completed {
    background-color: #2ecc71;
}

.novel-card .card-title {
    font-size: 1.1rem;
    line-height: 1.4;
    height: auto;
    max-height: 3em;
    overflow: hidden;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.novel-rating {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.chapter-item {
    margin: 0;
    padding: 0;
}

.chapter-item .btn-light {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    background-color: #f8f9fa;
    border: none;
    text-align: left;
    border-radius: 0;
    transition: all 0.2s ease;
    margin: 0;
    width: 100%;
    display: block;
}

.chapter-item:first-child .btn-light {
    border-radius: 4px 4px 0 0;
}

.chapter-item:last-child .btn-light {
    border-radius: 0 0 4px 4px;
}

/* Responsive */
@media (max-width: 768px) {
    .novel-card .card-img-top {
        height: 280px;
    }
}

@media (max-width: 576px) {
    .novel-card .card-img-top {
        height: 240px;
    }
    
    .novel-card .card-title {
        font-size: 1rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .novel-card {
        background: #2d3436;
        border-color: #444;
    }
    
    .novel-card .card-title a {
        color: #f1f1f1;
    }
    
    .chapter-item .btn-light {
        background-color: #3d4246;
        border: none;
        color: #f1f1f1;
    }
    
    .chapter-item .btn-light:hover {
        background-color: #454b4e;
    }
    
    .chapter-time {
        color: #888 !important;
    }
    
    .rating-value {
        color: #aaa;
    }
}

/* Container untuk chapter buttons */
.novel-meta {
    display: flex;
    flex-direction: column;
    gap: 0;
}

/* Popular Novels */
.popular-thumb {
    width: 50px;
    height: 70px;
    object-fit: cover;
    background-color: #f8f9fa;
    padding: 0;
    border-radius: 4px;
}

.list-group-item-action {
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.list-group-item-action:hover {
    background-color: #f8f9fa;
    transform: translateX(4px);
}

.list-group-item-action h6 {
    color: #2d3436;
    font-weight: 600;
    margin-bottom: 4px;
}

.list-group-item-action .small {
    font-size: 0.8rem;
}

.list-group-item-action .small i {
    font-size: 0.75rem;
    margin-right: 2px;
}

/* Dark mode support for popular novels */
@media (prefers-color-scheme: dark) {
    .list-group-item-action {
        background-color: #2d3436;
        border-color: #444;
    }
    
    .list-group-item-action:hover {
        background-color: #3d4246;
    }
    
    .list-group-item-action h6 {
        color: #f1f1f1;
    }
}
</style>

<?php get_footer(); ?>