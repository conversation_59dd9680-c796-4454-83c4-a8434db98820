<?php get_header(); ?>

<div class="container mt-4">
    <?php
    $current_genre = get_queried_object();
    ?>
    
    <!-- Genre Header -->
    <div class="genre-header mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="<?php echo home_url(); ?>">Home</a></li>
                <li class="breadcrumb-item"><a href="<?php echo home_url('/novel'); ?>">Novels</a></li>
                <li class="breadcrumb-item active"><?php echo $current_genre->name; ?></li>
            </ol>
        </nav>
        <h1 class="h4 mb-3"><?php echo $current_genre->name; ?> Novels</h1>
        <?php if ($current_genre->description) : ?>
            <p class="text-muted"><?php echo $current_genre->description; ?></p>
        <?php endif; ?>
    </div>

    <!-- Novels Grid -->
    <div class="row row-cols-2 row-cols-md-3 row-cols-lg-4 g-3">
        <?php
        if (have_posts()) : while (have_posts()) : the_post();
        ?>
        <div class="col">
            <div class="card h-100 novel-card">
                <?php if (has_post_thumbnail()) : ?>
                    <a href="<?php the_permalink(); ?>">
                        <?php the_post_thumbnail('novel-card', ['class' => 'card-img-top']); ?>
                    </a>
                <?php endif; ?>
                <div class="card-body p-2">
                    <h5 class="card-title h6 mb-2">
                        <a href="<?php the_permalink(); ?>" class="text-decoration-none text-truncate d-block" title="<?php the_title(); ?>"><?php the_title(); ?></a>
                    </h5>
                    <div class="novel-meta small">
                        <div class="d-flex align-items-center gap-2 mb-2">
                            <span class="text-muted"><i class="fas fa-eye"></i> <?php echo get_post_views(get_the_ID()); ?></span>
                            <?php 
                            $status = get_post_meta(get_the_ID(), '_novel_status', true);
                            echo '<span class="badge ' . ($status == 'Completed' ? 'bg-success' : 'bg-primary') . '">' . 
                                ($status ?: 'Ongoing') . '</span>';
                            ?>
                        </div>
                        <?php
                        $last_chapter = get_novel_last_chapter(get_the_ID());
                        if ($last_chapter) {
                            $chapter_number = get_post_meta($last_chapter->ID, '_chapter_number', true);
                            $novel_slug = get_post_field('post_name', get_the_ID());
                            $chapter_url = home_url("/{$novel_slug}/chapter-{$chapter_number}");
                            ?>
                            <a href="<?php echo esc_url($chapter_url); ?>" class="btn btn-sm btn-light text-truncate w-100">
                                Chapter <?php echo $chapter_number; ?>
                            </a>
                            <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
        <?php 
        endwhile;
        // Pagination
        echo '<div class="col-12 mt-4">';
        the_posts_pagination([
            'mid_size' => 2,
            'prev_text' => '<i class="fas fa-chevron-left"></i>',
            'next_text' => '<i class="fas fa-chevron-right"></i>',
            'class' => 'pagination justify-content-center'
        ]);
        echo '</div>';
        else :
            echo '<div class="col-12"><p class="text-center text-muted">No novels found in this genre.</p></div>';
        endif;
        ?>
    </div>
</div>

<style>
/* Novel Cards */
.novel-card {
    transition: transform 0.2s;
    border: 1px solid rgba(0,0,0,.125);
}

.novel-card:hover {
    transform: translateY(-2px);
}

.novel-card .card-img-top {
    height: 240px;
    object-fit: contain;
    background-color: #f8f9fa;
    padding: 8px;
}

.novel-card .card-title {
    font-size: 0.9rem;
    line-height: 1.3;
    height: 2.6em;
    overflow: hidden;
}

/* Pagination */
.pagination .page-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.pagination .page-item.active .page-link {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
}

/* Responsive */
@media (max-width: 768px) {
    .novel-card .card-img-top {
        height: 200px;
    }
}
</style>

<?php get_footer(); ?> 