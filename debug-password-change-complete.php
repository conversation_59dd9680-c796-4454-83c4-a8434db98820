<?php
/**
 * Complete Password Change Debug Tool
 * This script provides comprehensive debugging for the password change functionality
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Complete Password Change Debug</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
    .debug-output { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f8f9fa; }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="password"], input[type="text"] { 
        width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
    }
</style>';

// Debug function to trace password change
function debug_password_change_process($current_password, $new_password, $confirm_password) {
    echo '<div class="step">';
    echo '<h2>Step-by-Step Password Change Debug</h2>';
    
    if (!is_user_logged_in()) {
        echo '<p class="error">❌ User not logged in</p>';
        echo '</div>';
        return false;
    }
    
    $user = wp_get_current_user();
    echo "<p class='info'>👤 Current User: {$user->user_login} (ID: {$user->ID})</p>";
    
    // Step 1: Validate inputs
    echo '<h3>Step 1: Input Validation</h3>';
    $errors = array();
    
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $errors[] = 'Empty fields detected';
        echo '<p class="error">❌ Empty fields validation failed</p>';
    } else {
        echo '<p class="success">✅ All fields filled</p>';
    }
    
    if (strlen($new_password) < 6) {
        $errors[] = 'Password too short';
        echo '<p class="error">❌ Password length validation failed (minimum 6 characters)</p>';
    } else {
        echo '<p class="success">✅ Password length valid</p>';
    }
    
    if ($new_password !== $confirm_password) {
        $errors[] = 'Passwords do not match';
        echo '<p class="error">❌ Password confirmation failed</p>';
    } else {
        echo '<p class="success">✅ Password confirmation matches</p>';
    }
    
    // Step 2: Current password verification
    echo '<h3>Step 2: Current Password Verification</h3>';
    $current_valid = wp_check_password($current_password, $user->user_pass, $user->ID);
    if (!$current_valid) {
        $errors[] = 'Current password incorrect';
        echo '<p class="error">❌ Current password verification failed</p>';
        echo '<p class="info">Stored hash: ' . substr($user->user_pass, 0, 20) . '...</p>';
    } else {
        echo '<p class="success">✅ Current password verified</p>';
    }
    
    if (!empty($errors)) {
        echo '<div class="error">';
        echo '<h4>Validation Errors:</h4>';
        echo '<ul>';
        foreach ($errors as $error) {
            echo '<li>' . esc_html($error) . '</li>';
        }
        echo '</ul>';
        echo '</div>';
        echo '</div>';
        return false;
    }
    
    // Step 3: Password update
    echo '<h3>Step 3: Password Update Process</h3>';
    echo '<p class="info">🔄 Updating password...</p>';
    
    // Get original password hash
    $original_hash = $user->user_pass;
    echo '<p>Original hash: ' . substr($original_hash, 0, 30) . '...</p>';
    
    // Update password
    wp_set_password($new_password, $user->ID);
    
    // Step 4: Verify password update
    echo '<h3>Step 4: Password Update Verification</h3>';
    
    // Get updated user data
    $updated_user = get_user_by('id', $user->ID);
    $new_hash = $updated_user->user_pass;
    echo '<p>New hash: ' . substr($new_hash, 0, 30) . '...</p>';
    
    // Check if hash changed
    $hash_changed = ($original_hash !== $new_hash);
    echo '<p>Hash changed: ' . ($hash_changed ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . '</p>';
    
    // Test new password
    $new_password_valid = wp_check_password($new_password, $new_hash, $user->ID);
    echo '<p>New password valid: ' . ($new_password_valid ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . '</p>';
    
    // Test old password no longer works
    $old_password_invalid = !wp_check_password($current_password, $new_hash, $user->ID);
    echo '<p>Old password invalid: ' . ($old_password_invalid ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . '</p>';
    
    // Step 5: Database verification
    echo '<h3>Step 5: Database Verification</h3>';
    global $wpdb;
    $db_hash = $wpdb->get_var($wpdb->prepare("SELECT user_pass FROM {$wpdb->users} WHERE ID = %d", $user->ID));
    echo '<p>Database hash: ' . substr($db_hash, 0, 30) . '...</p>';
    echo '<p>Database matches: ' . ($db_hash === $new_hash ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . '</p>';
    
    // Final result
    $success = $hash_changed && $new_password_valid && $old_password_invalid && ($db_hash === $new_hash);
    
    echo '<h3>Final Result</h3>';
    if ($success) {
        echo '<p class="success">🎉 Password change completed successfully!</p>';
        
        // Re-authenticate user
        wp_clear_auth_cookie();
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID);
        echo '<p class="info">🔐 User re-authenticated</p>';
    } else {
        echo '<p class="error">❌ Password change failed!</p>';
    }
    
    echo '</div>';
    return $success;
}

// Handle test user creation
if (isset($_POST['create_test_user'])) {
    echo '<div class="step">';
    echo '<h2>Creating Test User</h2>';
    
    $test_username = 'pwdebug_' . time();
    $test_email = 'pwdebug_' . time() . '@example.com';
    $test_password = 'TestPass123';
    
    $user_id = wp_create_user($test_username, $test_password, $test_email);
    
    if (is_wp_error($user_id)) {
        echo "<p class='error'>Failed to create test user: " . $user_id->get_error_message() . "</p>";
    } else {
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        echo "<p class='success'>✅ Test user created and logged in</p>";
        echo "<p><strong>Username:</strong> {$test_username}</p>";
        echo "<p><strong>Password:</strong> {$test_password}</p>";
        echo "<p><strong>User ID:</strong> {$user_id}</p>";
    }
    echo '</div>';
}

// Handle password change test
if (isset($_POST['test_password_change'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    debug_password_change_process($current_password, $new_password, $confirm_password);
}

// Handle function test
if (isset($_POST['test_function_call'])) {
    echo '<div class="step">';
    echo '<h2>Testing Function Call</h2>';
    
    if (!is_user_logged_in()) {
        echo '<p class="error">No user logged in</p>';
    } else {
        // Simulate the exact function call
        $_POST['ln_reader_password_change_nonce'] = wp_create_nonce('ln_reader_password_change');
        $_POST['current_password'] = $_POST['test_current_password'];
        $_POST['new_password'] = $_POST['test_new_password'];
        $_POST['confirm_password'] = $_POST['test_confirm_password'];
        
        echo '<p class="info">Simulating function call with POST data...</p>';
        echo '<div class="debug-output">';
        echo 'POST data:' . "\n";
        foreach ($_POST as $key => $value) {
            if (strpos($key, 'password') !== false || strpos($key, 'nonce') !== false) {
                echo $key . ' = ' . (strpos($key, 'password') !== false ? str_repeat('*', strlen($value)) : $value) . "\n";
            }
        }
        echo '</div>';
        
        // Call the function directly
        if (function_exists('ln_reader_handle_password_change')) {
            echo '<p class="info">Calling ln_reader_handle_password_change()...</p>';
            
            // Capture any output
            ob_start();
            ln_reader_handle_password_change();
            $output = ob_get_clean();
            
            if (!empty($output)) {
                echo '<p>Function output:</p>';
                echo '<div class="debug-output">' . esc_html($output) . '</div>';
            } else {
                echo '<p class="info">Function executed (no output captured)</p>';
            }
        } else {
            echo '<p class="error">Function ln_reader_handle_password_change does not exist!</p>';
        }
    }
    echo '</div>';
}

// Current status
echo '<div class="step">';
echo '<h2>Current Status</h2>';

if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo "<p><strong>Logged in as:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
    echo "<p><strong>Email:</strong> {$current_user->user_email}</p>";
} else {
    echo "<p class='warning'>No user is currently logged in.</p>";
}

// Function status
$function_exists = function_exists('ln_reader_handle_password_change');
echo "<p><strong>Password change function exists:</strong> " . 
     ($function_exists ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . "</p>";

if ($function_exists) {
    $hooked = has_action('template_redirect', 'ln_reader_handle_password_change');
    echo "<p><strong>Function hooked to 'template_redirect':</strong> " . 
         ($hooked ? '<span class="success">✅ Yes</span>' : '<span class="error">❌ No</span>') . "</p>";
}
echo '</div>';

// Test forms
if (!is_user_logged_in()) {
    echo '<div class="step">';
    echo '<h2>Create Test User</h2>';
    echo '<form method="post">';
    echo '<button type="submit" name="create_test_user" class="btn">Create Test User & Login</button>';
    echo '</form>';
    echo '</div>';
} else {
    echo '<div class="step">';
    echo '<h2>Test Password Change</h2>';
    echo '<form method="post">';
    echo '<div class="form-group">';
    echo '<label for="current_password">Current Password:</label>';
    echo '<input type="password" id="current_password" name="current_password" required>';
    echo '</div>';
    echo '<div class="form-group">';
    echo '<label for="new_password">New Password:</label>';
    echo '<input type="password" id="new_password" name="new_password" required>';
    echo '</div>';
    echo '<div class="form-group">';
    echo '<label for="confirm_password">Confirm New Password:</label>';
    echo '<input type="password" id="confirm_password" name="confirm_password" required>';
    echo '</div>';
    echo '<button type="submit" name="test_password_change" class="btn">Test Password Change</button>';
    echo '</form>';
    echo '</div>';
    
    echo '<div class="step">';
    echo '<h2>Test Function Call</h2>';
    echo '<p>This tests the actual function call with proper nonce:</p>';
    echo '<form method="post">';
    echo '<div class="form-group">';
    echo '<label for="test_current_password">Current Password:</label>';
    echo '<input type="password" id="test_current_password" name="test_current_password" required>';
    echo '</div>';
    echo '<div class="form-group">';
    echo '<label for="test_new_password">New Password:</label>';
    echo '<input type="password" id="test_new_password" name="test_new_password" required>';
    echo '</div>';
    echo '<div class="form-group">';
    echo '<label for="test_confirm_password">Confirm New Password:</label>';
    echo '<input type="password" id="test_confirm_password" name="test_confirm_password" required>';
    echo '</div>';
    echo '<button type="submit" name="test_function_call" class="btn">Test Function Call</button>';
    echo '</form>';
    echo '</div>';
}

echo '<hr>';
echo '<p><em>Debug completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
