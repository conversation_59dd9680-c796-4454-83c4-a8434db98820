<?php
/**
 * Fix Authentication System
 * This script will diagnose and fix all authentication issues
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php', 
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Authentication System Fix</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
</style>';

echo '<div class="step">';
echo '<h2>Step 1: Enable WordPress Settings</h2>';

// Enable user registration
update_option('users_can_register', 1);
echo '<p class="success">✓ User registration enabled</p>';

// Set default role
update_option('default_role', 'subscriber');
echo '<p class="success">✓ Default role set to subscriber</p>';

// Check permalink structure
$permalink_structure = get_option('permalink_structure');
if (empty($permalink_structure)) {
    echo '<p class="warning">⚠ Permalink structure is empty. Please go to Settings > Permalinks and choose a structure.</p>';
} else {
    echo '<p class="success">✓ Permalink structure is set: ' . $permalink_structure . '</p>';
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 2: Create Authentication Pages</h2>';

$pages = array(
    'login' => array('title' => 'Login', 'template' => 'page-login.php'),
    'register' => array('title' => 'Register', 'template' => 'page-register.php'),
    'dashboard' => array('title' => 'Dashboard', 'template' => 'page-dashboard.php'),
    'reset-password' => array('title' => 'Reset Password', 'template' => 'page-reset-password.php')
);

foreach ($pages as $slug => $page_data) {
    $existing_page = get_page_by_path($slug);
    
    if ($existing_page) {
        echo "<p>Page '{$slug}' already exists (ID: {$existing_page->ID})</p>";
        
        // Ensure it's published
        if ($existing_page->post_status !== 'publish') {
            wp_update_post(array('ID' => $existing_page->ID, 'post_status' => 'publish'));
            echo "<p class='success'>✓ Published page '{$slug}'</p>";
        }
        
        // Set correct template
        $current_template = get_post_meta($existing_page->ID, '_wp_page_template', true);
        if ($current_template !== $page_data['template']) {
            update_post_meta($existing_page->ID, '_wp_page_template', $page_data['template']);
            echo "<p class='success'>✓ Updated template for '{$slug}' to {$page_data['template']}</p>";
        }
    } else {
        // Create new page
        $page_id = wp_insert_post(array(
            'post_title' => $page_data['title'],
            'post_name' => $slug,
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        ));
        
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', $page_data['template']);
            echo "<p class='success'>✓ Created page '{$slug}' (ID: {$page_id})</p>";
        } else {
            echo "<p class='error'>✗ Failed to create page '{$slug}'</p>";
        }
    }
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 3: Verify Template Files</h2>';

$theme_dir = get_template_directory();
$templates = array('page-login.php', 'page-register.php', 'page-dashboard.php', 'page-reset-password.php');

foreach ($templates as $template) {
    $path = $theme_dir . '/' . $template;
    if (file_exists($path)) {
        echo "<p class='success'>✓ {$template} exists</p>";
    } else {
        echo "<p class='error'>✗ {$template} missing!</p>";
    }
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 4: Flush Rewrite Rules</h2>';
flush_rewrite_rules();
echo '<p class="success">✓ Rewrite rules flushed</p>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 5: Test Authentication URLs</h2>';
$test_urls = array(
    'Login' => home_url('/login'),
    'Register' => home_url('/register'), 
    'Dashboard' => home_url('/dashboard'),
    'Reset Password' => home_url('/reset-password')
);

echo '<ul>';
foreach ($test_urls as $name => $url) {
    echo "<li><strong>{$name}:</strong> <a href='{$url}' target='_blank'>{$url}</a></li>";
}
echo '</ul>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 6: Debug Information</h2>';
echo '<p><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</p>';
echo '<p><strong>Theme:</strong> ' . get_template() . '</p>';
echo '<p><strong>Home URL:</strong> ' . home_url() . '</p>';
echo '<p><strong>Users Can Register:</strong> ' . (get_option('users_can_register') ? 'Yes' : 'No') . '</p>';
echo '<p><strong>Default Role:</strong> ' . get_option('default_role') . '</p>';

// Check if functions exist
$functions = array(
    'ln_reader_handle_login',
    'ln_reader_handle_registration', 
    'ln_reader_create_custom_pages',
    'ln_reader_hide_admin_bar'
);

echo '<p><strong>Authentication Functions:</strong></p>';
echo '<ul>';
foreach ($functions as $func) {
    $exists = function_exists($func);
    $status = $exists ? '<span class="success">✓</span>' : '<span class="error">✗</span>';
    echo "<li>{$status} {$func}</li>";
}
echo '</ul>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Next Steps</h2>';
echo '<ol>';
echo '<li>Visit the <a href="' . home_url('/login') . '" target="_blank">Login Page</a> and test the form</li>';
echo '<li>Visit the <a href="' . home_url('/register') . '" target="_blank">Register Page</a> and create a test account</li>';
echo '<li>Check that regular users cannot access <a href="' . admin_url() . '" target="_blank">WordPress Admin</a></li>';
echo '<li>Verify the <a href="' . home_url('/dashboard') . '" target="_blank">Dashboard</a> works for logged-in users</li>';
echo '</ol>';
echo '</div>';

echo '<hr>';
echo '<p><em>Fix completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
