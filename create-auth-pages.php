<?php
/**
 * Manual page creation script for LN Reader Authentication System
 * 
 * This script manually creates the required authentication pages.
 * Upload this file to your theme directory and run it once by visiting:
 * http://localhost/epic/wp-content/themes/lnreader/create-auth-pages.php
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo '<h1>LN Reader Authentication Pages Creator</h1>';
echo '<style>body { font-family: Arial, sans-serif; margin: 40px; } .success { color: green; } .error { color: red; } .info { color: blue; }</style>';

// Pages to create
$pages_to_create = array(
    'login' => array(
        'title' => 'Login',
        'template' => 'page-login.php'
    ),
    'register' => array(
        'title' => 'Register',
        'template' => 'page-register.php'
    ),
    'dashboard' => array(
        'title' => 'Dashboard',
        'template' => 'page-dashboard.php'
    ),
    'reset-password' => array(
        'title' => 'Reset Password',
        'template' => 'page-reset-password.php'
    )
);

echo '<h2>Creating Authentication Pages...</h2>';

foreach ($pages_to_create as $slug => $page_info) {
    echo "<h3>Processing: {$page_info['title']} ({$slug})</h3>";
    
    // Check if page already exists
    $existing_page = get_page_by_path($slug);
    
    if ($existing_page) {
        echo "<p class='info'>Page already exists with ID: {$existing_page->ID}</p>";
        
        // Update template if needed
        $current_template = get_post_meta($existing_page->ID, '_wp_page_template', true);
        if ($current_template !== $page_info['template']) {
            update_post_meta($existing_page->ID, '_wp_page_template', $page_info['template']);
            echo "<p class='success'>Updated template to: {$page_info['template']}</p>";
        } else {
            echo "<p class='info'>Template is already correct: {$page_info['template']}</p>";
        }
        
        echo "<p>URL: <a href='" . get_permalink($existing_page->ID) . "' target='_blank'>" . get_permalink($existing_page->ID) . "</a></p>";
    } else {
        // Create new page
        $page_data = array(
            'post_title'    => $page_info['title'],
            'post_content'  => '',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $slug,
            'post_author'   => get_current_user_id()
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            // Set template
            update_post_meta($page_id, '_wp_page_template', $page_info['template']);
            
            echo "<p class='success'>Successfully created page with ID: {$page_id}</p>";
            echo "<p class='success'>Template set to: {$page_info['template']}</p>";
            echo "<p>URL: <a href='" . get_permalink($page_id) . "' target='_blank'>" . get_permalink($page_id) . "</a></p>";
        } else {
            echo "<p class='error'>Failed to create page</p>";
            if (is_wp_error($page_id)) {
                echo "<p class='error'>Error: " . $page_id->get_error_message() . "</p>";
            }
        }
    }
    
    echo '<hr>';
}

// Enable user registration
echo '<h2>Configuring WordPress Settings...</h2>';

if (!get_option('users_can_register')) {
    update_option('users_can_register', 1);
    echo "<p class='success'>Enabled user registration</p>";
} else {
    echo "<p class='info'>User registration is already enabled</p>";
}

// Set default role
if (get_option('default_role') !== 'subscriber') {
    update_option('default_role', 'subscriber');
    echo "<p class='success'>Set default user role to subscriber</p>";
} else {
    echo "<p class='info'>Default user role is already subscriber</p>";
}

// Flush rewrite rules
flush_rewrite_rules();
echo "<p class='success'>Flushed rewrite rules</p>";

echo '<h2>Testing Page Templates...</h2>';

$theme_dir = get_template_directory();
$required_templates = array(
    'page-login.php',
    'page-register.php',
    'page-dashboard.php',
    'page-reset-password.php'
);

foreach ($required_templates as $template) {
    $template_path = $theme_dir . '/' . $template;
    if (file_exists($template_path)) {
        echo "<p class='success'>✓ {$template} exists</p>";
    } else {
        echo "<p class='error'>✗ {$template} is missing!</p>";
    }
}

echo '<h2>Summary</h2>';
echo '<p>All authentication pages have been processed. You can now test the system:</p>';
echo '<ul>';
echo '<li><a href="' . home_url('/login') . '" target="_blank">Login Page</a></li>';
echo '<li><a href="' . home_url('/register') . '" target="_blank">Register Page</a></li>';
echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Dashboard Page</a></li>';
echo '<li><a href="' . home_url('/reset-password') . '" target="_blank">Reset Password Page</a></li>';
echo '</ul>';

echo '<h3>Next Steps:</h3>';
echo '<ol>';
echo '<li>Test user registration by visiting the register page</li>';
echo '<li>Test user login by visiting the login page</li>';
echo '<li>Test the dashboard after logging in</li>';
echo '<li>Delete this file (create-auth-pages.php) after testing</li>';
echo '</ol>';

echo '<p><strong>Important:</strong> Delete this file after use for security reasons.</p>';

// Add some debugging info
echo '<h2>Debug Information</h2>';
echo '<p><strong>Home URL:</strong> ' . home_url() . '</p>';
echo '<p><strong>Theme Directory:</strong> ' . get_template_directory() . '</p>';
echo '<p><strong>Current User:</strong> ' . wp_get_current_user()->user_login . '</p>';
echo '<p><strong>Users Can Register:</strong> ' . (get_option('users_can_register') ? 'Yes' : 'No') . '</p>';
echo '<p><strong>Default Role:</strong> ' . get_option('default_role') . '</p>';

// Check for any WordPress errors
if (defined('WP_DEBUG') && WP_DEBUG) {
    echo '<p class="info">WordPress debug mode is enabled - check error logs for any issues.</p>';
}

echo '<hr>';
echo '<p><em>Script completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
