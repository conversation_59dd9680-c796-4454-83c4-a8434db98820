<?php
/**
 * Test Password Change Functionality
 * This script tests the password change process step by step
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Password Change Test</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="text"], input[type="password"] { 
        width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
    }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
    .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f8f9fa; }
</style>';

// Handle test user creation
if (isset($_POST['create_test_user'])) {
    echo '<div class="step">';
    echo '<h2>Creating Test User</h2>';
    
    $test_username = 'pwtest_' . time();
    $test_email = 'pwtest_' . time() . '@example.com';
    $test_password = 'TestPass123';
    
    $user_id = wp_create_user($test_username, $test_password, $test_email);
    
    if (is_wp_error($user_id)) {
        echo "<p class='error'>Failed to create test user: " . $user_id->get_error_message() . "</p>";
    } else {
        echo "<p class='success'>Test user created successfully!</p>";
        echo "<p><strong>Username:</strong> {$test_username}</p>";
        echo "<p><strong>Password:</strong> {$test_password}</p>";
        echo "<p><strong>User ID:</strong> {$user_id}</p>";
        
        // Auto-login the test user
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        echo "<p class='info'>Test user has been logged in automatically.</p>";
    }
    echo '</div>';
}

// Handle password change test
if (isset($_POST['test_password_change'])) {
    echo '<div class="step">';
    echo '<h2>Testing Password Change</h2>';
    
    if (!is_user_logged_in()) {
        echo "<p class='error'>No user is logged in. Please create and login with a test user first.</p>";
    } else {
        $current_user = wp_get_current_user();
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        
        echo "<p><strong>Current User:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
        echo "<p><strong>Testing password change...</strong></p>";
        
        // Test current password verification
        $current_valid = wp_check_password($current_password, $current_user->user_pass, $current_user->ID);
        echo "<p><strong>Current password valid:</strong> " . ($current_valid ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</p>";
        
        if ($current_valid) {
            // Update password
            echo "<p>Updating password...</p>";
            wp_set_password($new_password, $current_user->ID);
            
            // Verify password was updated
            $updated_user = get_user_by('id', $current_user->ID);
            $new_valid = wp_check_password($new_password, $updated_user->user_pass, $current_user->ID);
            echo "<p><strong>New password valid:</strong> " . ($new_valid ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</p>";
            
            // Test old password no longer works
            $old_invalid = !wp_check_password($current_password, $updated_user->user_pass, $current_user->ID);
            echo "<p><strong>Old password invalid:</strong> " . ($old_invalid ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . "</p>";
            
            if ($new_valid && $old_invalid) {
                echo "<p class='success'>✓ Password change test successful!</p>";
                
                // Re-authenticate user
                wp_clear_auth_cookie();
                wp_set_current_user($current_user->ID);
                wp_set_auth_cookie($current_user->ID);
                echo "<p class='info'>User re-authenticated with new password.</p>";
            } else {
                echo "<p class='error'>✗ Password change test failed!</p>";
            }
        } else {
            echo "<p class='error'>Cannot test password change - current password is incorrect.</p>";
        }
    }
    echo '</div>';
}

// Handle cleanup
if (isset($_POST['cleanup_test_users'])) {
    echo '<div class="step">';
    echo '<h2>Cleaning Up Test Users</h2>';
    
    global $wpdb;
    $test_users = $wpdb->get_results("SELECT ID, user_login FROM {$wpdb->users} WHERE user_login LIKE 'pwtest_%'");
    
    if (empty($test_users)) {
        echo "<p class='info'>No test users found to clean up.</p>";
    } else {
        foreach ($test_users as $user) {
            $deleted = wp_delete_user($user->ID);
            if ($deleted) {
                echo "<p class='success'>✓ Deleted user: {$user->user_login}</p>";
            } else {
                echo "<p class='error'>✗ Failed to delete user: {$user->user_login}</p>";
            }
        }
    }
    
    // Logout current user if it's a test user
    $current_user = wp_get_current_user();
    if ($current_user && strpos($current_user->user_login, 'pwtest_') === 0) {
        wp_logout();
        echo "<p class='info'>Logged out test user.</p>";
    }
    echo '</div>';
}

echo '<div class="step">';
echo '<h2>Current Status</h2>';

if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo "<p><strong>Logged in as:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
    echo "<p><strong>Email:</strong> {$current_user->user_email}</p>";
    echo "<p><strong>Roles:</strong> " . implode(', ', $current_user->roles) . "</p>";
} else {
    echo "<p class='warning'>No user is currently logged in.</p>";
}

// Check if password change function exists
$function_exists = function_exists('ln_reader_handle_password_change');
echo "<p><strong>Password change function exists:</strong> " . 
     ($function_exists ? '<span class="success">✓ Yes</span>' : '<span class="error">✗ No</span>') . "</p>";

if ($function_exists) {
    // Check if function is hooked
    $hooked = has_action('template_redirect', 'ln_reader_handle_password_change');
    echo "<p><strong>Function hooked to 'template_redirect':</strong> " . 
         ($hooked ? '<span class="success">✓ Yes</span>' : '<span class="error">✗ No</span>') . "</p>";
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Test Actions</h2>';

if (!is_user_logged_in()) {
    echo '<form method="post" style="margin-bottom: 20px;">';
    echo '<button type="submit" name="create_test_user" class="btn">Create Test User & Login</button>';
    echo '</form>';
} else {
    echo '<h3>Test Password Change</h3>';
    echo '<form method="post">';
    echo '<div class="form-group">';
    echo '<label for="current_password">Current Password:</label>';
    echo '<input type="password" id="current_password" name="current_password" placeholder="Enter current password" required>';
    echo '</div>';
    echo '<div class="form-group">';
    echo '<label for="new_password">New Password:</label>';
    echo '<input type="password" id="new_password" name="new_password" placeholder="Enter new password" required>';
    echo '</div>';
    echo '<button type="submit" name="test_password_change" class="btn">Test Password Change</button>';
    echo '</form>';
}

echo '<form method="post" style="margin-top: 20px;">';
echo '<button type="submit" name="cleanup_test_users" class="btn" style="background: #dc3545;">Clean Up Test Users</button>';
echo '</form>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Dashboard Links</h2>';
echo '<ul>';
echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Visit Dashboard</a></li>';
echo '<li><a href="' . home_url('/dashboard#password') . '" target="_blank">Visit Dashboard Password Tab</a></li>';
echo '<li><a href="' . admin_url('users.php') . '" target="_blank">WordPress Users Admin</a></li>';
echo '</ul>';
echo '</div>';

echo '<hr>';
echo '<p><em>Test completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
