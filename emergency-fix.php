<?php
/**
 * EMERGENCY RECOVERY SCRIPT
 * 
 * This script will temporarily disable the authentication system to restore your site.
 * Upload this file to your theme directory and run it immediately:
 * http://localhost/epic/wp-content/themes/lnreader/emergency-fix.php
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin (if possible)
if (!current_user_can('administrator')) {
    // If we can't check admin status due to the error, allow access for emergency
    echo '<h1 style="color: red;">EMERGENCY RECOVERY MODE</h1>';
    echo '<p>Site is in emergency recovery mode. Proceeding with fixes...</p>';
}

echo '<h1>LN Reader Emergency Recovery</h1>';
echo '<style>body { font-family: Arial, sans-serif; margin: 40px; } .success { color: green; } .error { color: red; } .warning { color: orange; }</style>';

echo '<h2>Step 1: Backing up functions.php</h2>';

$theme_dir = get_template_directory();
$functions_file = $theme_dir . '/functions.php';
$backup_file = $theme_dir . '/functions-backup-' . date('Y-m-d-H-i-s') . '.php';

// Create backup
if (file_exists($functions_file)) {
    if (copy($functions_file, $backup_file)) {
        echo '<p class="success">✓ Backup created: ' . basename($backup_file) . '</p>';
    } else {
        echo '<p class="error">✗ Failed to create backup</p>';
    }
} else {
    echo '<p class="error">✗ functions.php not found</p>';
}

echo '<h2>Step 2: Identifying the Problem</h2>';

// Read the functions.php file
$functions_content = file_get_contents($functions_file);

// Check for duplicate function definitions
$issues_found = array();

// Look for duplicate session start functions
if (substr_count($functions_content, 'function start_session') > 1) {
    $issues_found[] = 'Duplicate start_session function';
}

// Look for duplicate hooks
if (substr_count($functions_content, "add_action('init', 'start_session'") > 1) {
    $issues_found[] = 'Duplicate start_session hook';
}

// Look for conflicting login redirects
$login_redirect_count = substr_count($functions_content, 'ln_reader_custom_login_redirect') + 
                       substr_count($functions_content, 'ln_reader_intercept_login_page') + 
                       substr_count($functions_content, 'ln_reader_wp_loaded_redirect');

if ($login_redirect_count > 3) {
    $issues_found[] = 'Multiple conflicting login redirect functions';
}

if (!empty($issues_found)) {
    echo '<p class="error">Issues found:</p>';
    echo '<ul>';
    foreach ($issues_found as $issue) {
        echo '<li class="error">' . $issue . '</li>';
    }
    echo '</ul>';
} else {
    echo '<p class="success">No obvious issues detected in functions.php</p>';
}

echo '<h2>Step 3: Creating Clean Functions.php</h2>';

// Create a clean version by removing the authentication system
$clean_content = $functions_content;

// Remove the entire authentication system block
$start_marker = '// ========================================';
$end_marker = 'add_action(\'after_switch_theme\', \'flush_feed_rewrite_rules\');';

$start_pos = strpos($clean_content, $start_marker);
$end_pos = strpos($clean_content, $end_marker);

if ($start_pos !== false && $end_pos !== false) {
    // Find the actual end position after the marker
    $end_pos = $end_pos + strlen($end_marker);
    
    // Remove the authentication system
    $before = substr($clean_content, 0, $start_pos);
    $after = substr($clean_content, $end_pos);
    
    $clean_content = $before . $after;
    
    echo '<p class="warning">Removed authentication system from functions.php</p>';
} else {
    echo '<p class="error">Could not locate authentication system markers</p>';
}

// Write the clean version
$clean_file = $theme_dir . '/functions-clean.php';
if (file_put_contents($clean_file, $clean_content)) {
    echo '<p class="success">✓ Created clean functions.php as functions-clean.php</p>';
} else {
    echo '<p class="error">✗ Failed to create clean functions.php</p>';
}

echo '<h2>Step 4: Recovery Options</h2>';

echo '<div style="background: #f0f0f0; padding: 20px; margin: 20px 0; border-left: 4px solid #007cba;">';
echo '<h3>Choose Recovery Method:</h3>';

echo '<p><strong>Option 1: Automatic Recovery (Recommended)</strong></p>';
echo '<p><a href="?action=auto_fix" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Auto Fix Now</a></p>';

echo '<p><strong>Option 2: Manual Recovery</strong></p>';
echo '<ol>';
echo '<li>Rename current functions.php to functions-broken.php</li>';
echo '<li>Rename functions-clean.php to functions.php</li>';
echo '<li>Test your site</li>';
echo '<li>Re-implement authentication system step by step</li>';
echo '</ol>';

echo '<p><strong>Option 3: Complete Restore</strong></p>';
echo '<p>If you have a recent backup of functions.php, restore it and start over.</p>';

echo '</div>';

// Handle auto fix
if (isset($_GET['action']) && $_GET['action'] === 'auto_fix') {
    echo '<h2>Performing Automatic Fix...</h2>';
    
    // Step 1: Rename broken functions.php
    $broken_file = $theme_dir . '/functions-broken-' . date('Y-m-d-H-i-s') . '.php';
    if (rename($functions_file, $broken_file)) {
        echo '<p class="success">✓ Renamed broken functions.php to ' . basename($broken_file) . '</p>';
    } else {
        echo '<p class="error">✗ Failed to rename broken functions.php</p>';
    }
    
    // Step 2: Use clean version
    if (file_exists($clean_file)) {
        if (copy($clean_file, $functions_file)) {
            echo '<p class="success">✓ Restored clean functions.php</p>';
        } else {
            echo '<p class="error">✗ Failed to restore clean functions.php</p>';
        }
    }
    
    // Step 3: Test the site
    echo '<h3>Testing Site Recovery...</h3>';
    
    $site_url = home_url();
    echo '<p>Testing site access: <a href="' . $site_url . '" target="_blank">' . $site_url . '</a></p>';
    
    // Try to make a simple request to test
    $response = wp_remote_get($site_url, array('timeout' => 10));
    if (!is_wp_error($response)) {
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code === 200) {
            echo '<p class="success">✓ Site is responding normally (HTTP 200)</p>';
        } else {
            echo '<p class="warning">⚠ Site responding with HTTP ' . $response_code . '</p>';
        }
    } else {
        echo '<p class="error">✗ Could not test site response</p>';
    }
    
    echo '<h3>Recovery Complete!</h3>';
    echo '<p class="success">Your site should now be accessible. The authentication system has been temporarily disabled.</p>';
    
    echo '<h3>Next Steps:</h3>';
    echo '<ol>';
    echo '<li>Test your site: <a href="' . $site_url . '" target="_blank">Visit Homepage</a></li>';
    echo '<li>Test admin access: <a href="' . admin_url() . '" target="_blank">Visit Admin</a></li>';
    echo '<li>Delete this emergency script for security</li>';
    echo '<li>Contact support to properly re-implement authentication system</li>';
    echo '</ol>';
}

echo '<h2>Files Created:</h2>';
echo '<ul>';
if (file_exists($backup_file)) {
    echo '<li>Backup: ' . basename($backup_file) . '</li>';
}
if (file_exists($clean_file)) {
    echo '<li>Clean version: ' . basename($clean_file) . '</li>';
}
echo '</ul>';

echo '<h2>Important Notes:</h2>';
echo '<ul>';
echo '<li class="warning">Delete this emergency script after recovery</li>';
echo '<li class="warning">The authentication system is temporarily disabled</li>';
echo '<li class="warning">Users will need to use wp-login.php until system is restored</li>';
echo '<li>All backups are preserved for safety</li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Emergency recovery completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
