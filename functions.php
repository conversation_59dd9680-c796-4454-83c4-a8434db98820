<?php
// Include the navwalker
require_once get_template_directory() . '/class-wp-bootstrap-navwalker.php';

if (!defined('ABSPATH')) {
    exit;
}

// Register Novel post type
function register_novel_post_type() {
    $labels = array(
        'name' => 'Novels',
        'singular_name' => 'Novel',
        'add_new' => 'Add New Novel',
        'add_new_item' => 'Add New Novel',
        'edit_item' => 'Edit Novel',
        'new_item' => 'New Novel',
        'view_item' => 'View Novel',
        'search_items' => 'Search Novels',
        'not_found' => 'No novels found',
        'not_found_in_trash' => 'No novels found in trash',
        'menu_name' => 'Novels'
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'novel'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-book',
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'comments'),
        'show_in_rest'       => true
    );
    
    register_post_type('novel', $args);
    
    // Remove excerpt meta box
    remove_post_type_support('novel', 'excerpt');

    // Flush rewrite rules only once
    if (!get_option('novel_rewrite_rules_flushed')) {
        flush_rewrite_rules();
        update_option('novel_rewrite_rules_flushed', true);
    }
}
add_action('init', 'register_novel_post_type', 0);

// Filter novel permalink
function modify_novel_permalink($permalink, $post) {
    if ($post->post_type === 'novel') {
        return str_replace('/novel/novel/', '/novel/', $permalink);
    }
    return $permalink;
}
add_filter('post_type_link', 'modify_novel_permalink', 10, 2);

// Add custom rewrite rules
function add_novel_rewrite_rules() {
    add_rewrite_rule(
        '^novel/?$',
        'index.php?post_type=novel',
        'top'
    );
    add_rewrite_rule(
        '^novel/([^/]+)/?$',
        'index.php?novel=$matches[1]',
        'top'
    );
    
    flush_rewrite_rules();
}
add_action('init', 'add_novel_rewrite_rules', 10);

// Theme Setup
function ln_reader_setup() {
    // Add theme support
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    add_theme_support('html5', array('search-form', 'comment-form', 'comment-list', 'gallery', 'caption'));

    // Register Menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'ln-reader'),
        'footer' => __('Footer Menu', 'ln-reader'),
    ));

    // Custom image sizes
    add_image_size('novel-cover', 200, 280, true);

    // Add Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');

    // Register Novel Tags taxonomy
    register_taxonomy('novel_tag', 'novel', array(
        'labels' => array(
            'name' => 'Novel Tags',
            'singular_name' => 'Novel Tag',
            'search_items' => 'Search Tags',
            'popular_items' => 'Popular Tags',
            'all_items' => 'All Tags',
            'parent_item' => null,
            'parent_item_colon' => null,
            'edit_item' => 'Edit Tag',
            'update_item' => 'Update Tag',
            'add_new_item' => 'Add New Tag',
            'new_item_name' => 'New Tag Name',
            'add_or_remove_items' => 'Add or Remove Tags',
            'choose_from_most_used' => 'Choose from most used tags',
            'menu_name' => 'Tags'
        ),
        'hierarchical' => false,
        'show_ui' => true,
        'show_in_rest' => true,
        'update_count_callback' => '_update_post_term_count',
        'query_var' => true,
        'rewrite' => array('slug' => 'novel-tag')
    ));

    // Register Genres taxonomy
    register_taxonomy('novel_genre', 'novel', array(
        'hierarchical' => true,
        'labels' => array(
            'name' => 'Genres',
            'singular_name' => 'Genre',
            'search_items' => 'Search Genres',
            'all_items' => 'All Genres',
            'parent_item' => 'Parent Genre',
            'parent_item_colon' => 'Parent Genre:',
            'edit_item' => 'Edit Genre',
            'update_item' => 'Update Genre',
            'add_new_item' => 'Add New Genre',
            'new_item_name' => 'New Genre Name',
            'menu_name' => 'Genres'
        ),
        'show_ui' => true,
        'show_in_rest' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'genre')
    ));
}
add_action('after_setup_theme', 'ln_reader_setup');

// Enqueue scripts and styles
function ln_reader_scripts() {
    // Enqueue Bootstrap CSS
    wp_enqueue_style('bootstrap', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');
    
    // Enqueue Bootstrap Icons CSS
    wp_enqueue_style('bootstrap-icons', 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css');
    
    // Enqueue theme styles
    wp_enqueue_style('ln-reader-style', get_stylesheet_uri());
    wp_enqueue_style('latest-releases', get_template_directory_uri() . '/css/latest-releases.css', array(), '1.0.0');
    wp_enqueue_style('footer-style', get_template_directory_uri() . '/css/footer.css', array(), '1.0.0');
    wp_enqueue_style('chapter-buttons', get_template_directory_uri() . '/css/chapter-buttons.css', array(), '1.0.0');
    
    // Enqueue Bootstrap JS and its dependencies
    wp_enqueue_script('bootstrap-bundle', 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js', array('jquery'), '5.3.0', true);
    
    // Enqueue theme scripts
    wp_enqueue_script('ln-reader-script', get_template_directory_uri() . '/js/main.js', array('jquery'), '1.0.0', true);

    // Localize script for AJAX functionality
    wp_localize_script('ln-reader-script', 'ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ln_reader_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'ln_reader_scripts');

// Add rewrite rules for chapters and novels
function add_chapter_rewrite_rules() {
    // Novel single page rule
    add_rewrite_rule(
        'novel/([^/]+)/?$',
        'index.php?post_type=novel&name=$matches[1]',
        'top'
    );
    
    // Chapter page rule
    add_rewrite_rule(
        '([^/]+)/chapter-([0-9]+)/?$',
        'index.php?name=$matches[1]-chapter-$matches[2]',
        'top'
    );
}
add_action('init', 'add_chapter_rewrite_rules');

// Handle chapter requests
function ln_reader_handle_chapter_request($query) {
    if (!is_admin() && $query->is_main_query()) {
        $post_name = $query->get('name');

        if ($post_name && preg_match('/^(.+)-chapter-(\d+)$/', $post_name, $matches)) {
            $novel_name = $matches[1];
            $chapter_number = $matches[2];

            // Find the novel by slug
            $novel = get_page_by_path($novel_name, OBJECT, 'novel');
            if ($novel) {
                // Find chapter by meta query
                $args = array(
                    'post_type' => 'post',
                    'posts_per_page' => 1,
                    'meta_query' => array(
                        'relation' => 'AND',
                        array(
                            'key' => '_novel_id',
                            'value' => $novel->ID,
                            'compare' => '='
                        ),
                        array(
                            'key' => '_chapter_number',
                            'value' => $chapter_number,
                            'compare' => '=',
                            'type' => 'NUMERIC'
                        )
                    )
                );

                $chapters = get_posts($args);
                if (!empty($chapters)) {
                    $query->set('p', $chapters[0]->ID);
                    $query->set('post_type', 'post');
                    $query->set('page', '');
                    $query->is_404 = false;
                    $query->is_page = false;
                    $query->is_single = true;
                    $query->is_singular = true;
                }
            }
        }
    }
    return $query;
}
add_action('pre_get_posts', 'ln_reader_handle_chapter_request', 999);

// Modify permalink structure for chapters
function modify_chapter_permalink($permalink, $post) {
    if ($post->post_type !== 'post') {
        return $permalink;
    }

    // Get novel ID and chapter number
    $novel_id = get_post_meta($post->ID, '_novel_id', true);
    $chapter_number = get_post_meta($post->ID, '_chapter_number', true);
    
    if (!$novel_id || !$chapter_number) {
        return $permalink;
    }

    // Get novel post
    $novel = get_post($novel_id);
    if (!$novel) {
        return $permalink;
    }

    // Create new permalink structure
    return home_url("/{$novel->post_name}/chapter-{$chapter_number}");
}
add_filter('post_link', 'modify_chapter_permalink', 10, 2);

// Set post name when saving chapter
function set_chapter_post_name($data) {
    if ($data['post_type'] !== 'post' || empty($_POST['novel_id']) || empty($_POST['chapter_number'])) {
        return $data;
    }

    // If post title is manually set and not empty, respect it
    if (!empty($data['post_title']) && $data['post_title'] !== 'Auto Draft') {
        return $data;
    }

    $novel = get_post($_POST['novel_id']);
    if ($novel) {
        $volume = !empty($_POST['volume_number']) ? ' Volume ' . $_POST['volume_number'] : '';
        $data['post_title'] = $novel->post_title . $volume . ' - Chapter ' . $_POST['chapter_number'];
    }

    return $data;
}
add_filter('wp_insert_post_data', 'set_chapter_post_name', 10, 1);

// Save chapter meta data
function save_chapter_meta($post_id) {
    if (!isset($_POST['chapter_details_nonce']) || 
        !wp_verify_nonce($_POST['chapter_details_nonce'], 'chapter_details_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    // Save novel ID
    if (isset($_POST['novel_id'])) {
        $novel_id = sanitize_text_field($_POST['novel_id']);
        update_post_meta($post_id, '_novel_id', $novel_id);
        
        // Get novel slug
        $novel = get_post($novel_id);
        if ($novel) {
            $novel_slug = $novel->post_name;
            
            // Save chapter number and update post name
            if (isset($_POST['chapter_number'])) {
                $chapter_number = sanitize_text_field($_POST['chapter_number']);
                update_post_meta($post_id, '_chapter_number', $chapter_number);
                
                // Update post name
                remove_action('save_post', 'save_chapter_meta');
                wp_update_post(array(
                    'ID' => $post_id,
                    'post_name' => $novel_slug . '-chapter-' . $chapter_number
                ));
                add_action('save_post', 'save_chapter_meta');
            }
        }
    }
    
    // Save volume number
    if (isset($_POST['volume_number'])) {
        update_post_meta($post_id, '_volume_number', sanitize_text_field($_POST['volume_number']));
    }
    
    // Save chapter title
    if (isset($_POST['chapter_title'])) {
        update_post_meta($post_id, '_chapter_title', sanitize_text_field($_POST['chapter_title']));
    }
}
add_action('save_post', 'save_chapter_meta');

// Helper Functions
function get_time_ago($datetime) {
    return human_time_diff(strtotime($datetime), current_time('timestamp')) . ' ago';
}

function get_chapter_url($novel_id, $chapter_number) {
    $novel = get_post($novel_id);
    return home_url("/{$novel->post_name}/chapter/{$chapter_number}");
}

function get_novel_url($novel_id) {
    $novel = get_post($novel_id);
    return home_url("/{$novel->post_name}");
}

function trim_synopsis($content, $length = 500) {
    $content = wp_strip_all_tags($content);
    if (strlen($content) > $length) {
        $content = substr($content, 0, $length);
        $content = substr($content, 0, strrpos($content, ' '));
        $content .= '... <button class="read-more">read more</button>';
    }
    return $content;
}

function get_novel_rating($novel_id) {
    // Pastikan novel_id valid
    $novel_id = absint($novel_id);
    if (!$novel_id) {
        return array(
            'average' => 0.0,
            'count' => 0
        );
    }

    // Get meta values with strict type checking
    $total_rating = get_post_meta($novel_id, '_total_rating', true);
    $rating_count = get_post_meta($novel_id, '_rating_count', true);
    $average_rating = get_post_meta($novel_id, '_average_rating', true);

    // Set default values if empty or invalid
    if (!is_numeric($total_rating)) $total_rating = 0;
    if (!is_numeric($rating_count)) $rating_count = 0;
    if (!is_numeric($average_rating)) $average_rating = 0;

    // Convert to proper types
    $total = floatval($total_rating);
    $count = absint($rating_count);
    $average = floatval($average_rating);

    // Recalculate average if needed
    if ($count > 0 && $total > 0) {
        $average = round($total / $count, 1);
    }

    // Ensure average is between 0 and 5
    $average = max(0, min(5, $average));

    return array(
        'average' => $average,
        'count' => $count
    );
}

// Add meta boxes for novel details
function add_novel_meta_boxes() {
    add_meta_box(
        'novel_details',
        'Novel Details',
        'novel_details_callback',
        'novel',
        'normal',
        'high'
    );
    
    // Remove default custom fields meta box
    remove_meta_box('postcustom', 'novel', 'normal');
}
add_action('add_meta_boxes', 'add_novel_meta_boxes');

function novel_details_callback($post) {
    wp_nonce_field('novel_details_nonce', 'novel_details_nonce');
    
    // Get current values
    $author = get_post_meta($post->ID, '_author', true);
    $status = get_post_meta($post->ID, '_novel_status', true);
    $alternative_title = get_post_meta($post->ID, '_alternative_title', true);
    $native_language = get_post_meta($post->ID, '_native_language', true);
    $release_year = get_post_meta($post->ID, '_release_year', true);
    $novelupdates_url = get_post_meta($post->ID, '_novelupdates_url', true);
    ?>
    <div class="novel-meta-box">
        <p>
            <label for="novel_author"><strong>Author:</strong></label><br>
            <input type="text" id="novel_author" name="author" value="<?php echo esc_attr($author); ?>" class="widefat">
        </p>
        <p>
            <label for="alternative_title"><strong>Alternative Title:</strong></label><br>
            <input type="text" id="alternative_title" name="alternative_title" value="<?php echo esc_attr($alternative_title); ?>" class="widefat">
        </p>
        <p>
            <label for="native_language"><strong>Native Language:</strong></label><br>
            <input type="text" id="native_language" name="native_language" value="<?php echo esc_attr($native_language); ?>" class="widefat">
        </p>
        <p>
            <label for="release_year"><strong>Release Year:</strong></label><br>
            <input type="number" id="release_year" name="release_year" value="<?php echo esc_attr($release_year); ?>" class="widefat" min="1900" max="<?php echo date('Y'); ?>">
        </p>
        <p>
            <label for="novelupdates_url"><strong>NovelUpdates URL:</strong></label><br>
            <input type="url" id="novelupdates_url" name="novelupdates_url" value="<?php echo esc_url($novelupdates_url); ?>" class="widefat">
        </p>
        <p>
            <label for="novel_status"><strong>Status:</strong></label><br>
            <select id="novel_status" name="novel_status" class="widefat">
                <option value="ongoing" <?php selected($status, 'ongoing'); ?>>Ongoing</option>
                <option value="completed" <?php selected($status, 'completed'); ?>>Completed</option>
                <option value="hiatus" <?php selected($status, 'hiatus'); ?>>Hiatus</option>
                <option value="dropped" <?php selected($status, 'dropped'); ?>>Dropped</option>
            </select>
        </p>
    </div>
    <?php
}

// Save novel meta data
function save_novel_meta($post_id) {
    if (!isset($_POST['novel_details_nonce']) || !wp_verify_nonce($_POST['novel_details_nonce'], 'novel_details_nonce')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Save author (save to both meta keys untuk kompatibilitas)
    if (isset($_POST['author'])) {
        $author = sanitize_text_field($_POST['author']);
        update_post_meta($post_id, '_author', $author);
        update_post_meta($post_id, '_novel_author', $author);
    }

    // Save other fields
    $fields = array(
        'novel_status',
        'alternative_title',
        'native_language',
        'release_year',
        'novelupdates_url'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = sanitize_text_field($_POST[$field]);
            update_post_meta($post_id, '_' . $field, $value);
        }
    }
}
add_action('save_post_novel', 'save_novel_meta');

// Add meta box for chapter details
function add_chapter_meta_boxes() {
    add_meta_box(
        'chapter_details',
        __('Chapter Details', 'lnreader'),
        'chapter_details_callback',
        'post', // Change back to post type
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_chapter_meta_boxes');

function chapter_details_callback($post) {
    wp_nonce_field('chapter_details_nonce', 'chapter_details_nonce');
    
    $novel_id = get_post_meta($post->ID, '_novel_id', true);
    $volume_number = get_post_meta($post->ID, '_volume_number', true);
    $chapter_number = get_post_meta($post->ID, '_chapter_number', true);
    $chapter_title = get_post_meta($post->ID, '_chapter_title', true);
    
    // Get all novels
    $novels = get_posts(array(
        'post_type' => 'novel',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
    ));
    ?>
    <div class="chapter-details">
        <p>
            <label for="novel_id"><?php _e('Select Novel:', 'lnreader'); ?></label><br>
            <select name="novel_id" id="novel_id" class="widefat" required>
                <option value=""><?php _e('-- Select Novel --', 'lnreader'); ?></option>
                <?php foreach ($novels as $novel) : ?>
                    <option value="<?php echo $novel->ID; ?>" <?php selected($novel_id, $novel->ID); ?>>
                        <?php echo $novel->post_title; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </p>
        <p>
            <label for="volume_number"><?php _e('Volume Number:', 'lnreader'); ?></label><br>
            <input type="number" id="volume_number" name="volume_number" class="widefat" min="0" step="1" 
                value="<?php echo esc_attr($volume_number); ?>" 
                placeholder="<?php esc_attr_e('Optional', 'lnreader'); ?>">
        </p>
        <p>
            <label for="chapter_number"><?php _e('Chapter Number:', 'lnreader'); ?></label><br>
            <input type="number" id="chapter_number" name="chapter_number" class="widefat" required min="0" step="1" 
                value="<?php echo esc_attr($chapter_number); ?>"
                placeholder="<?php esc_attr_e('Required', 'lnreader'); ?>">
        </p>
        <p>
            <label for="chapter_title"><?php _e('Chapter Title:', 'lnreader'); ?></label><br>
            <input type="text" id="chapter_title" name="chapter_title" class="widefat" 
                value="<?php echo esc_attr($chapter_title); ?>"
                placeholder="<?php esc_attr_e('Optional', 'lnreader'); ?>">
        </p>
    </div>
    <style>
    .chapter-details p {
        margin: 1em 0;
    }
    .chapter-details label {
        display: inline-block;
        margin-bottom: 0.5em;
    }
    .chapter-details input[required],
    .chapter-details select[required] {
        border-left: 4px solid #007cba;
    }
    </style>
    <?php
}

// Automatically set chapter title based on novel, volume, and chapter number
function set_chapter_title($data) {
    if ($data['post_type'] !== 'post' || empty($_POST['novel_id']) || empty($_POST['chapter_number'])) {
        return $data;
    }

    // If post title is manually set and not empty, respect it
    if (!empty($data['post_title']) && $data['post_title'] !== 'Auto Draft') {
        return $data;
    }

    $novel = get_post($_POST['novel_id']);
    if ($novel) {
        $volume = !empty($_POST['volume_number']) ? ' Volume ' . $_POST['volume_number'] : '';
        $data['post_title'] = $novel->post_title . $volume . ' - Chapter ' . $_POST['chapter_number'];
    }

    return $data;
}
add_filter('wp_insert_post_data', 'set_chapter_title', 10, 1);

// Add custom columns to chapters admin list
function ln_reader_chapter_columns($columns) {
    // Create a new columns array with only the desired columns
    $new_columns = array();

    // Add checkbox for bulk actions
    if (isset($columns['cb'])) {
        $new_columns['cb'] = $columns['cb'];
    }

    // Add title column
    $new_columns['title'] = __('Title', 'lnreader');

    // Add author column
    $new_columns['author'] = __('Author', 'lnreader');

    // Add categories column
    if (isset($columns['categories'])) {
        $new_columns['categories'] = $columns['categories'];
    }

    // Add tags column
    if (isset($columns['tags'])) {
        $new_columns['tags'] = $columns['tags'];
    }

    // Add a custom icon column (the icon shown in your screenshot)
    $new_columns['status_icon'] = '';

    // Add date column
    if (isset($columns['date'])) {
        $new_columns['date'] = $columns['date'];
    }

    // Add views column
    $new_columns['views'] = __('Views', 'lnreader');

    // Add another views column (as shown in your screenshot)
    $new_columns['views2'] = __('Views', 'lnreader');

    return $new_columns;
}
add_filter('manage_post_posts_columns', 'ln_reader_chapter_columns');

// Add content to custom columns
function ln_reader_chapter_custom_column($column, $post_id) {
    switch ($column) {
        case 'status_icon':
            // Add a simple icon or status indicator
            echo '<span class="dashicons dashicons-media-text" style="color: #666;"></span>';
            break;
        case 'views':
            $views = get_post_meta($post_id, '_view_count', true);
            if (!$views) {
                $views = get_post_meta($post_id, 'post_views', true); // Alternative meta key
            }
            echo $views ? number_format($views) : '0';
            break;
        case 'views2':
            // Second views column (duplicate for layout matching)
            $views = get_post_meta($post_id, '_view_count', true);
            if (!$views) {
                $views = get_post_meta($post_id, 'post_views', true); // Alternative meta key
            }
            echo $views ? number_format($views) : '0';
            break;
    }
}
add_action('manage_post_posts_custom_column', 'ln_reader_chapter_custom_column', 10, 2);

// Make columns sortable
function ln_reader_chapter_sortable_columns($columns) {
    $columns['views'] = 'views';
    $columns['views2'] = 'views2';
    return $columns;
}
add_filter('manage_edit-post_sortable_columns', 'ln_reader_chapter_sortable_columns');

// Handle custom column sorting
function ln_reader_chapter_custom_orderby($query) {
    if (!is_admin()) {
        return;
    }

    $orderby = $query->get('orderby');

    switch ($orderby) {
        case 'views':
        case 'views2':
            $query->set('meta_key', '_view_count');
            $query->set('orderby', 'meta_value_num');
            break;
    }
}
add_action('pre_get_posts', 'ln_reader_chapter_custom_orderby');

// Enqueue admin styles for chapter management
function ln_reader_admin_styles() {
    $screen = get_current_screen();
    if ($screen && $screen->id === 'edit-post') {
        wp_enqueue_style('ln-reader-admin', get_template_directory_uri() . '/css/admin-style.css', array(), '1.0.0');
    }
}
add_action('admin_enqueue_scripts', 'ln_reader_admin_styles');

// Add AJAX actions
add_action('wp_ajax_save_novel_rating', 'save_novel_rating');

function save_novel_rating() {
    if (!is_user_logged_in()) {
        wp_send_json_error(__('Please login to rate this novel', 'lnreader'));
    }

    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $rating = isset($_POST['rating']) ? intval($_POST['rating']) : 0;
    $user_id = get_current_user_id();
    
    // Check if rating is valid
    if (!$post_id || $rating < 1 || $rating > 5) {
        wp_send_json_error(__('Invalid rating', 'lnreader'));
    }

    // Check if user already rated
    $user_ratings = get_post_meta($post_id, '_user_ratings', true) ?: array();
    if (isset($user_ratings[$user_id])) {
        wp_send_json_error(__('You have already rated this novel', 'lnreader'));
    }

    // Get current rating data
    $total_rating = floatval(get_post_meta($post_id, '_total_rating', true)) ?: 0;
    $rating_count = intval(get_post_meta($post_id, '_rating_count', true)) ?: 0;

    // Update total rating and count
    $new_total = $total_rating + $rating;
    $new_count = $rating_count + 1;
    
    // Calculate average
    $average = round($new_total / $new_count, 1);

    // Save the rating
    update_post_meta($post_id, '_total_rating', $new_total);
    update_post_meta($post_id, '_rating_count', $new_count);
    update_post_meta($post_id, '_average_rating', $average);

    // Save user rating
    $user_ratings[$user_id] = $rating;
    update_post_meta($post_id, '_user_ratings', $user_ratings);

    wp_send_json_success([
        'average' => $average,
        'count' => $new_count,
        'message' => __('Rating saved successfully', 'lnreader')
    ]);
}

function has_user_rated($post_id) {
    if (!is_user_logged_in() || empty($post_id)) {
        return false;
    }
    
    $user_id = get_current_user_id();
    $user_ratings = get_post_meta($post_id, '_user_ratings', true);
    
    // Pastikan user_ratings adalah array
    if (empty($user_ratings) || !is_array($user_ratings)) {
        return false;
    }
    
    return isset($user_ratings[$user_id]) && !empty($user_ratings[$user_id]);
}

function get_user_reading_progress($novel_id) {
    if (!is_user_logged_in()) {
        return false;
    }
    
    $user_id = get_current_user_id();
    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
    
    // Ensure reading_progress is an array
    if (!is_array($reading_progress)) {
        $reading_progress = array();
    }
    
    if (empty($reading_progress) || !isset($reading_progress[$novel_id])) {
        return false;
    }
    
    return $reading_progress[$novel_id];
}

function get_latest_chapter($novel_id) {
    $latest_chapter = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'orderby' => 'date',
        'order' => 'DESC',
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field' => 'name',
                'terms' => get_the_title($novel_id)
            )
        )
    ));
    
    return !empty($latest_chapter) ? $latest_chapter[0] : false;
}

function get_latest_chapters($novel_id, $limit = 3) {
    $latest_chapters = get_posts(array(
        'post_type' => 'post',
        'posts_per_page' => $limit,
        'orderby' => 'date',
        'order' => 'DESC',
        'tax_query' => array(
            array(
                'taxonomy' => 'category',
                'field' => 'name',
                'terms' => get_the_title($novel_id)
            )
        )
    ));
    
    return !empty($latest_chapters) ? $latest_chapters : false;
}

// Novel Score Functions
function get_novel_score($novel_id) {
    $score = get_post_meta($novel_id, '_novel_score', true);
    return $score ? floatval($score) : 0;
}

// Novel Views Functions
function get_novel_views($novel_id) {
    $views = get_post_meta($novel_id, '_novel_views', true);
    return $views ? intval($views) : 0;
}

function increment_novel_views($novel_id) {
    $views = get_novel_views($novel_id);
    update_post_meta($novel_id, '_novel_views', $views + 1);
}

// Time Format Functions
function time_elapsed_string($datetime) {
    $now = current_time('timestamp');
    $ago = strtotime($datetime);
    $diff = $now - $ago;

    if ($diff < 60) {
        return 'just now';
    }

    $intervals = array(
        1                => array('year', 31556926),
        $diff < 31556926 => array('month', 2629744),
        $diff < 2629744  => array('week', 604800),
        $diff < 604800   => array('day', 86400),
        $diff < 86400    => array('hour', 3600),
        $diff < 3600     => array('minute', 60),
        $diff < 60       => array('second', 1)
    );

    foreach ($intervals as $condition => $interval) {
        if ($condition) {
            $count = floor($diff / $interval[1]);
            return $count . ' ' . $interval[0] . ($count > 1 ? 's' : '') . ' ago';
        }
    }
}

function truncate_title($title, $length = 40) {
    if (mb_strlen($title) <= $length) {
        return $title;
    }
    return mb_substr($title, 0, $length) . '...';
}

// Automatically add novel title as category when creating new novel
function add_novel_title_as_category($post_id) {
    // Verify if this is a novel post type
    if (get_post_type($post_id) !== 'novel') {
        return;
    }

    // Get the novel title
    $novel_title = get_the_title($post_id);

    // Check if category exists
    $cat = get_term_by('name', $novel_title, 'category');
    
    if (!$cat) {
        // Create new category with novel title
        $new_cat = wp_insert_term(
            $novel_title,
            'category',
            array(
                'description' => __('Category for novel: ', 'lnreader') . $novel_title,
                'slug' => sanitize_title($novel_title)
            )
        );

        if (!is_wp_error($new_cat)) {
            // Assign the category to the novel
            wp_set_post_categories($post_id, array($new_cat['term_id']), true);
        }
    } else {
        // Assign existing category to the novel
        wp_set_post_categories($post_id, array($cat->term_id), true);
    }
}
add_action('wp_insert_post', 'add_novel_title_as_category', 10, 1);

// Customize default post type labels
function customize_post_type_labels() {
    global $wp_post_types;
    
    // Customize 'post' post type labels
    $labels = &$wp_post_types['post']->labels;
    $labels->name = __('Chapters', 'lnreader');
    $labels->singular_name = __('Chapter', 'lnreader');
    $labels->add_new = __('Add Chapter', 'lnreader');
    $labels->add_new_item = __('Add New Chapter', 'lnreader');
    $labels->edit_item = __('Edit Chapter', 'lnreader');
    $labels->new_item = __('New Chapter', 'lnreader');
    $labels->view_item = __('View Chapter', 'lnreader');
    $labels->view_items = __('View Chapters', 'lnreader');
    $labels->search_items = __('Search Chapters', 'lnreader');
    $labels->not_found = __('No chapters found', 'lnreader');
    $labels->not_found_in_trash = __('No chapters found in trash', 'lnreader');
    $labels->all_items = __('All Chapters', 'lnreader');
    $labels->archives = __('Chapter Archives', 'lnreader');
    $labels->attributes = __('Chapter Attributes', 'lnreader');
    $labels->insert_into_item = __('Insert into chapter', 'lnreader');
    $labels->uploaded_to_this_item = __('Uploaded to this chapter', 'lnreader');
    $labels->filter_items_list = __('Filter chapters list', 'lnreader');
    $labels->items_list_navigation = __('Chapters list navigation', 'lnreader');
    $labels->items_list = __('Chapters list', 'lnreader');
    $labels->menu_name = __('Chapters', 'lnreader');
    $labels->name_admin_bar = __('Chapter', 'lnreader');
    $labels->item_published = __('Chapter published', 'lnreader');
    $labels->item_published_privately = __('Chapter published privately', 'lnreader');
    $labels->item_reverted_to_draft = __('Chapter reverted to draft', 'lnreader');
    $labels->item_scheduled = __('Chapter scheduled', 'lnreader');
    $labels->item_updated = __('Chapter updated', 'lnreader');
}
add_action('init', 'customize_post_type_labels', 999);

// Change "Enter title here" placeholder for posts
function change_post_title_placeholder($title) {
    $screen = get_current_screen();
    if ('post' == $screen->post_type) {
        $title = __('Enter post title', 'lnreader');
    }
    return $title;
}
add_filter('enter_title_here', 'change_post_title_placeholder');

// Customize admin menu order
function custom_menu_order($menu_ord) {
    if (!$menu_ord) return true;
    
    return array(
        'index.php', // Dashboard
        'edit.php?post_type=novel', // Novels
        'edit.php', // Posts (now Chapter)
        'upload.php', // Media
        'edit.php?post_type=page', // Pages
        'edit-comments.php', // Comments
        'themes.php', // Appearance
        'plugins.php', // Plugins
        'users.php', // Users
        'tools.php', // Tools
        'options-general.php', // Settings
    );
}
add_filter('custom_menu_order', '__return_true');
add_filter('menu_order', 'custom_menu_order');

// Register menu pages
function ln_reader_admin_menu() {
    // Remove default Posts menu
    remove_menu_page('edit.php');
    
    // Add custom Chapters menu using default post type
    add_menu_page(
        __('Chapters', 'lnreader'),
        __('Chapters', 'lnreader'),
        'edit_posts',
        'edit.php',
        '',
        'dashicons-media-text',
        20
    );
}
add_action('admin_menu', 'ln_reader_admin_menu');

// Create Bookmarks page on theme activation
function create_bookmarks_page() {
    $bookmarks_page = get_page_by_path('bookmarks');
    
    if (!$bookmarks_page) {
        $page_data = array(
            'post_title'    => 'Bookmarks',
            'post_content'  => '',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => 'bookmarks'
        );
        
        $page_id = wp_insert_post($page_data);
        
        if ($page_id && !is_wp_error($page_id)) {
            update_post_meta($page_id, '_wp_page_template', 'page-bookmarks.php');
            
            // Add to primary menu
            $locations = get_theme_mod('nav_menu_locations');
            if (!empty($locations['primary'])) {
                $menu_id = $locations['primary'];
                $menu_item_data = array(
                    'menu-item-object-id' => $page_id,
                    'menu-item-object'    => 'page',
                    'menu-item-type'      => 'post_type',
                    'menu-item-status'    => 'publish',
                    'menu-item-title'     => 'Bookmarks',
                    'menu-item-url'       => get_permalink($page_id)
                );
                wp_update_nav_menu_item($menu_id, 0, $menu_item_data);
            }
        }
    }
}
add_action('after_switch_theme', 'create_bookmarks_page');

// Bookmark functions
function toggle_bookmark() {
    // Verify nonce
    if (!check_ajax_referer('bookmark_nonce', 'security', false)) {
        wp_send_json_error('Invalid security token');
        return;
    }

    if (!is_user_logged_in()) {
        wp_send_json_error('Please login to bookmark novels');
        return;
    }

    $novel_id = isset($_POST['novel_id']) ? intval($_POST['novel_id']) : 0;

    if (!$novel_id) {
        wp_send_json_error('Invalid novel ID');
        return;
    }

    $user_id = get_current_user_id();
    $bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
    if (!is_array($bookmarked_novels)) {
        $bookmarked_novels = array();
    }

    $is_bookmarked = in_array($novel_id, $bookmarked_novels);

    if ($is_bookmarked) {
        $bookmarked_novels = array_diff($bookmarked_novels, array($novel_id));
        $message = 'Novel removed from bookmarks';
        $action = 'removed';
    } else {
        $bookmarked_novels[] = $novel_id;
        $message = 'Novel added to bookmarks';
        $action = 'added';
    }

    $result = update_user_meta($user_id, 'bookmarked_novels', array_values($bookmarked_novels));

    wp_send_json_success(array(
        'message' => $message,
        'action' => $action
    ));
}

add_action('wp_ajax_toggle_bookmark', 'toggle_bookmark');
add_action('wp_ajax_nopriv_toggle_bookmark', function() {
    wp_send_json_error('Please login to bookmark novels');
});

// AJAX handler for loading more novels
function load_more_novels() {
    $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
    $posts_per_page = 12;

    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'post_status' => 'publish'
    );

    $query = new WP_Query($args);

    if ($query->have_posts()) {
        ob_start();
        while ($query->have_posts()) {
            $query->the_post();
            // Include novel card template or output HTML
            ?>
            <div class="col-md-4 mb-4">
                <div class="card novel-card h-100">
                    <div class="card-img-top-wrapper">
                        <?php if (has_post_thumbnail()) : ?>
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('medium', array('class' => 'card-img-top')); ?>
                            </a>
                        <?php else : ?>
                            <div class="placeholder-img">
                                <i class="fas fa-book"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                        </h5>
                        <p class="card-text"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                    </div>
                </div>
            </div>
            <?php
        }
        wp_reset_postdata();
        $html = ob_get_clean();

        wp_send_json_success(array('html' => $html));
    } else {
        wp_send_json_error('No more novels found');
    }
}
add_action('wp_ajax_load_more_novels', 'load_more_novels');
add_action('wp_ajax_nopriv_load_more_novels', 'load_more_novels');

// AJAX handler for novel search
function search_novels() {
    $query = isset($_POST['query']) ? sanitize_text_field($_POST['query']) : '';

    if (empty($query)) {
        wp_send_json_error('Empty search query');
        return;
    }

    $args = array(
        'post_type' => 'novel',
        'posts_per_page' => 10,
        's' => $query,
        'post_status' => 'publish'
    );

    $search_query = new WP_Query($args);
    $results = array();

    if ($search_query->have_posts()) {
        while ($search_query->have_posts()) {
            $search_query->the_post();
            $results[] = array(
                'title' => get_the_title(),
                'url' => get_permalink(),
                'thumbnail' => has_post_thumbnail() ? get_the_post_thumbnail_url(get_the_ID(), 'thumbnail') : get_template_directory_uri() . '/images/default-cover.jpg',
                'author' => get_post_meta(get_the_ID(), '_novel_author', true) ?: 'Unknown Author'
            );
        }
        wp_reset_postdata();
    }

    wp_send_json_success($results);
}
add_action('wp_ajax_search_novels', 'search_novels');
add_action('wp_ajax_nopriv_search_novels', 'search_novels');

// Bookmark script functionality is handled inline in single-novel.php
// No separate bookmark.js file needed since functionality is already implemented

// Reading Progress Tracking Functions
function update_reading_progress() {
    if (!check_ajax_referer('reading_progress_nonce', 'nonce', false)) {
        wp_send_json_error('Invalid nonce');
        return;
    }

    $user_id = get_current_user_id();
    $novel_id = intval($_POST['novel_id']);
    $chapter_number = intval($_POST['chapter_number']);
    
    if (!$novel_id || !$chapter_number) {
        wp_send_json_error('Invalid data');
        return;
    }

    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
    if (!is_array($reading_progress)) {
        $reading_progress = array();
    }

    $reading_progress[$novel_id] = $chapter_number;
    update_user_meta($user_id, 'reading_progress', $reading_progress);

    wp_send_json_success();
}
add_action('wp_ajax_update_reading_progress', 'update_reading_progress');
add_action('wp_ajax_nopriv_update_reading_progress', 'update_reading_progress');

// Reading progress functionality is handled by:
// - Server-side automatic tracking (save_reading_progress function)
// - Visual progress bar in main.js
// - AJAX functionality in auth.js
// No separate reading-progress.js file needed

// Add custom columns to novel post type
function ln_reader_novel_columns($columns) {
    $new_columns = array();
    foreach ($columns as $key => $value) {
        if ($key === 'title') {
            $new_columns[$key] = $value;
            $new_columns['author'] = __('Author', 'lnreader');
            $new_columns['posted_by'] = __('Posted by', 'lnreader');
            $new_columns['views'] = __('Views', 'lnreader');
            $new_columns['last_chapter'] = __('Last Chapter', 'lnreader');
        } else if ($key !== 'author') { // Skip default author column
            $new_columns[$key] = $value;
        }
    }
    return $new_columns;
}
add_filter('manage_novel_posts_columns', 'ln_reader_novel_columns');

// Fill novel custom columns
function ln_reader_novel_custom_column($column, $post_id) {
    switch ($column) {
        case 'author':
            $author = get_post_meta($post_id, 'author', true);
            echo $author ? esc_html($author) : '—';
            break;
        case 'posted_by':
            $author_id = get_post_field('post_author', $post_id);
            echo get_the_author_meta('display_name', $author_id);
            break;
        case 'views':
            $views = get_post_meta($post_id, '_view_count', true);
            echo $views ? number_format($views) : '0';
            break;
        case 'last_chapter':
            $chapters = get_posts(array(
                'post_type' => 'post',
                'posts_per_page' => 1,
                'meta_query' => array(
                    array(
                        'key' => '_novel_id',
                        'value' => $post_id,
                        'compare' => '='
                    )
                ),
                'meta_key' => '_chapter_number',
                'orderby' => 'meta_value_num',
                'order' => 'DESC'
            ));
            
            if (!empty($chapters)) {
                $chapter = $chapters[0];
                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                echo "Chapter " . $chapter_number;
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_novel_posts_custom_column', 'ln_reader_novel_custom_column', 10, 2);

// Make novel columns sortable
function ln_reader_novel_sortable_columns($columns) {
    $columns['author'] = 'author';
    $columns['posted_by'] = 'author';
    $columns['views'] = 'views';
    $columns['last_chapter'] = 'last_chapter';
    return $columns;
}
add_filter('manage_edit-novel_sortable_columns', 'ln_reader_novel_sortable_columns');

// Handle novel columns sorting
function ln_reader_novel_custom_orderby($query) {
    if (!is_admin() || !$query->is_main_query() || $query->get('post_type') !== 'novel') {
        return;
    }

    $orderby = $query->get('orderby');
    switch ($orderby) {
        case 'author':
            $query->set('meta_key', 'author');
            $query->set('orderby', 'meta_value');
            break;
        case 'views':
            $query->set('meta_key', '_view_count');
            $query->set('orderby', 'meta_value_num');
            break;
    }
}
add_action('pre_get_posts', 'ln_reader_novel_custom_orderby');

/**
 * Get next chapter based on chapter number
 */
function get_next_chapter($current_chapter_id, $novel_id) {
    $current_chapter_number = intval(get_post_meta($current_chapter_id, '_chapter_number', true));
    
    // Query untuk mencari chapter berikutnya
    $args = array(
        'post_type' => 'post', // Ubah dari 'chapter' ke 'post'
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel_id
            ),
            array(
                'key' => '_chapter_number',
                'value' => $current_chapter_number,
                'compare' => '>',
                'type' => 'NUMERIC'
            )
        ),
        'orderby' => 'meta_value_num',
        'meta_key' => '_chapter_number',
        'order' => 'ASC'
    );

    $next_chapter = get_posts($args);
    
    if (!empty($next_chapter)) {
        return $next_chapter[0];
    }
    
    return false;
}

/**
 * Get previous chapter based on chapter number
 */
function get_prev_chapter($current_chapter_id, $novel_id) {
    $current_chapter_number = intval(get_post_meta($current_chapter_id, '_chapter_number', true));
    
    // Query untuk mencari chapter sebelumnya
    $args = array(
        'post_type' => 'post', // Ubah dari 'chapter' ke 'post'
        'posts_per_page' => 1,
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'value' => $novel_id
            ),
            array(
                'key' => '_chapter_number',
                'value' => $current_chapter_number,
                'compare' => '<',
                'type' => 'NUMERIC'
            )
        ),
        'orderby' => 'meta_value_num',
        'meta_key' => '_chapter_number',
        'order' => 'DESC'
    );

    $prev_chapter = get_posts($args);
    
    if (!empty($prev_chapter)) {
        return $prev_chapter[0];
    }
    
    return false;
}

// Save reading progress when viewing a chapter
function save_reading_progress($post_id) {
    if (!is_user_logged_in() || !is_single()) {
        return;
    }

    $novel_id = get_post_meta($post_id, '_novel_id', true);
    if (!$novel_id) {
        return;
    }

    $user_id = get_current_user_id();
    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
    if (!is_array($reading_progress)) {
        $reading_progress = array();
    }

    $reading_progress[$novel_id] = $post_id;
    update_user_meta($user_id, 'reading_progress', $reading_progress);
}
add_action('wp', function() {
    if (is_single()) {
        save_reading_progress(get_the_ID());
    }
});

// Fungsi untuk menghitung views
function set_post_views() {
    // Check if we're on a single post or single novel page
    if (!is_single() && !is_singular('novel')) {
        return;
    }

    $post_id = get_the_ID();

    // Cek apakah sudah ada session untuk post ini
    if (!isset($_SESSION['post_viewed'])) {
        $_SESSION['post_viewed'] = array();
    }

    // Jika post belum dilihat dalam session ini
    if (!in_array($post_id, $_SESSION['post_viewed'])) {
        $count = get_post_meta($post_id, 'post_views', true);

        if ($count == '') {
            delete_post_meta($post_id, 'post_views');
            add_post_meta($post_id, 'post_views', '1');
        } else {
            $count++;
            update_post_meta($post_id, 'post_views', $count);
        }

        // Tandai post sudah dilihat dalam session ini
        $_SESSION['post_viewed'][] = $post_id;
    }
}

// ========================================
// CUSTOM USER AUTHENTICATION & DASHBOARD SYSTEM
// ========================================

// Hide admin bar for non-admin users
function ln_reader_hide_admin_bar() {
    if (!current_user_can('administrator') && !is_admin()) {
        show_admin_bar(false);
    }
}
add_action('after_setup_theme', 'ln_reader_hide_admin_bar');

// Redirect non-admin users away from WordPress backend
function ln_reader_redirect_non_admin_users() {
    if (is_admin() && !current_user_can('administrator') && !wp_doing_ajax()) {
        wp_redirect(home_url('/dashboard'));
        exit;
    }
}
add_action('admin_init', 'ln_reader_redirect_non_admin_users');

// Custom login redirect
function ln_reader_login_redirect($redirect_to, $request, $user) {
    if (isset($user->roles) && is_array($user->roles)) {
        if (in_array('administrator', $user->roles)) {
            return admin_url();
        } else {
            return home_url('/dashboard');
        }
    }
    return $redirect_to;
}
add_filter('login_redirect', 'ln_reader_login_redirect', 10, 3);

// Custom logout redirect
function ln_reader_logout_redirect() {
    wp_redirect(home_url('/login'));
    exit;
}
add_action('wp_logout', 'ln_reader_logout_redirect');

// Enable user registration
function ln_reader_enable_registration() {
    update_option('users_can_register', 1);
    update_option('default_role', 'subscriber');

    // Log the settings change
    error_log('LN Reader: Registration enabled. users_can_register=' . get_option('users_can_register') . ', default_role=' . get_option('default_role'));
}
add_action('after_switch_theme', 'ln_reader_enable_registration');

// Force enable registration on every admin page load (temporary for debugging)
function ln_reader_force_enable_registration() {
    if (is_admin() && current_user_can('administrator')) {
        $current_setting = get_option('users_can_register');
        if (!$current_setting) {
            update_option('users_can_register', 1);
            error_log('LN Reader: Force enabled user registration');
        }

        $current_role = get_option('default_role');
        if ($current_role !== 'subscriber') {
            update_option('default_role', 'subscriber');
            error_log('LN Reader: Force set default role to subscriber');
        }
    }
}
add_action('admin_init', 'ln_reader_force_enable_registration');

// Create custom pages on theme activation
function ln_reader_create_custom_pages() {
    $pages = array(
        'login' => array(
            'title' => 'Login',
            'template' => 'page-login.php'
        ),
        'register' => array(
            'title' => 'Register',
            'template' => 'page-register.php'
        ),
        'dashboard' => array(
            'title' => 'Dashboard',
            'template' => 'page-dashboard.php'
        ),
        'reset-password' => array(
            'title' => 'Reset Password',
            'template' => 'page-reset-password.php'
        )
    );

    foreach ($pages as $slug => $page_data) {
        $existing_page = get_page_by_path($slug);
        if (!$existing_page) {
            $page_id = wp_insert_post(array(
                'post_title' => $page_data['title'],
                'post_name' => $slug,
                'post_content' => '',
                'post_status' => 'publish',
                'post_type' => 'page',
                'post_author' => 1
            ));

            if ($page_id && !is_wp_error($page_id)) {
                update_post_meta($page_id, '_wp_page_template', $page_data['template']);
            }
        }
    }

    // Flush rewrite rules
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'ln_reader_create_custom_pages');

// Force create pages on admin init (temporary for debugging)
function ln_reader_force_create_pages() {
    if (is_admin() && current_user_can('administrator') && isset($_GET['create_auth_pages'])) {
        // Enable registration
        update_option('users_can_register', 1);
        update_option('default_role', 'subscriber');

        // Create pages
        ln_reader_create_custom_pages();

        // Add admin notice
        add_action('admin_notices', function() {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>Authentication pages created successfully!</strong></p>';
            echo '<p>Test URLs:</p>';
            echo '<ul>';
            echo '<li><a href="' . home_url('/login') . '" target="_blank">Login Page</a></li>';
            echo '<li><a href="' . home_url('/register') . '" target="_blank">Register Page</a></li>';
            echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Dashboard Page</a></li>';
            echo '</ul>';
            echo '</div>';
        });
    }
}
add_action('admin_init', 'ln_reader_force_create_pages');

// Add admin menu item for creating auth pages
function ln_reader_add_auth_admin_menu() {
    add_management_page(
        'Authentication Setup',
        'Auth Setup',
        'manage_options',
        'ln-reader-auth-setup',
        'ln_reader_auth_setup_page'
    );
}
add_action('admin_menu', 'ln_reader_add_auth_admin_menu');

function ln_reader_auth_setup_page() {
    echo '<div class="wrap">';
    echo '<h1>LN Reader Authentication Setup</h1>';

    if (isset($_GET['setup']) && $_GET['setup'] === 'run') {
        // Run setup
        update_option('users_can_register', 1);
        update_option('default_role', 'subscriber');
        ln_reader_create_custom_pages();
        flush_rewrite_rules();

        echo '<div class="notice notice-success"><p><strong>Setup completed!</strong></p></div>';
    }

    echo '<p>This tool will set up the custom authentication system for your LN Reader theme.</p>';
    echo '<p><a href="?page=ln-reader-auth-setup&setup=run" class="button button-primary">Run Authentication Setup</a></p>';

    // Show current status
    echo '<h2>Current Status</h2>';
    echo '<table class="widefat">';
    echo '<tr><th>Setting</th><th>Status</th></tr>';
    echo '<tr><td>User Registration</td><td>' . (get_option('users_can_register') ? '✓ Enabled' : '✗ Disabled') . '</td></tr>';
    echo '<tr><td>Default Role</td><td>' . get_option('default_role') . '</td></tr>';

    $pages = array('login', 'register', 'dashboard', 'reset-password');
    foreach ($pages as $slug) {
        $page = get_page_by_path($slug);
        $status = $page ? '✓ Exists (ID: ' . $page->ID . ')' : '✗ Missing';
        echo "<tr><td>{$slug} page</td><td>{$status}</td></tr>";
    }
    echo '</table>';

    echo '<h2>Test Links</h2>';
    echo '<ul>';
    echo '<li><a href="' . home_url('/login') . '" target="_blank">Login Page</a></li>';
    echo '<li><a href="' . home_url('/register') . '" target="_blank">Register Page</a></li>';
    echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Dashboard Page</a></li>';
    echo '<li><a href="' . home_url('/reset-password') . '" target="_blank">Reset Password Page</a></li>';
    echo '</ul>';

    echo '</div>';
}

// Handle custom login
function ln_reader_handle_login() {
    // Only process on login page
    if (!is_page('login')) {
        return;
    }

    if (isset($_POST['ln_reader_login_nonce']) && wp_verify_nonce($_POST['ln_reader_login_nonce'], 'ln_reader_login')) {
        $username = sanitize_text_field($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']);

        // Debug logging
        error_log('LN Reader Login Attempt: ' . $username);

        if (empty($username) || empty($password)) {
            wp_redirect(add_query_arg('login_error', urlencode('Please fill in all fields.'), home_url('/login')));
            exit;
        }

        $user = wp_authenticate($username, $password);

        if (is_wp_error($user)) {
            error_log('LN Reader Login Failed: ' . $user->get_error_message());
            wp_redirect(add_query_arg('login_error', urlencode($user->get_error_message()), home_url('/login')));
            exit;
        }

        // Success
        error_log('LN Reader Login Success: ' . $user->user_login);
        wp_clear_auth_cookie();
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID, $remember);

        // Redirect based on user role
        if (user_can($user, 'administrator')) {
            wp_redirect(admin_url());
        } else {
            wp_redirect(home_url('/dashboard'));
        }
        exit;
    }
}
add_action('template_redirect', 'ln_reader_handle_login');

// Handle custom registration
function ln_reader_handle_registration() {
    // Check if this is a POST request with our nonce
    if (!isset($_POST['ln_reader_register_nonce']) || !wp_verify_nonce($_POST['ln_reader_register_nonce'], 'ln_reader_register')) {
        return;
    }

    // Additional check: only process if we're on register page or if the form was submitted
    global $post;
    $is_register_page = (is_page('register') || (isset($post) && $post->post_name === 'register') ||
                        (isset($_POST['ln_reader_register_nonce']) && strpos($_SERVER['REQUEST_URI'], 'register') !== false));

    if (!$is_register_page) {
        error_log('LN Reader Registration: Not on register page. URI: ' . $_SERVER['REQUEST_URI']);
        return;
    }

    $username = sanitize_user($_POST['username']);
    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    error_log('LN Reader Registration Attempt: ' . $username . ' / ' . $email . ' on URI: ' . $_SERVER['REQUEST_URI']);

    $errors = array();

    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        $errors[] = 'Please fill in all fields.';
    }

    if (!is_email($email)) {
        $errors[] = 'Please enter a valid email address.';
    }

    if (username_exists($username)) {
        $errors[] = 'Username already exists.';
    }

    if (email_exists($email)) {
        $errors[] = 'Email already registered.';
    }

    if (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }

    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }

    if (!empty($errors)) {
        error_log('LN Reader Registration Validation Errors: ' . implode(', ', $errors));
        if (!session_id()) session_start();
        $_SESSION['registration_errors'] = $errors;
        wp_redirect(home_url('/register'));
        exit;
    }

    // Check if user registration is enabled
    if (!get_option('users_can_register')) {
        error_log('LN Reader Registration: User registration is disabled');
        if (!session_id()) session_start();
        $_SESSION['registration_errors'] = array('User registration is currently disabled.');
        wp_redirect(home_url('/register'));
        exit;
    }

    // Create user
    error_log('LN Reader Registration: Attempting to create user with wp_create_user()');
    $user_id = wp_create_user($username, $password, $email);

    if (is_wp_error($user_id)) {
        error_log('LN Reader Registration Failed: ' . $user_id->get_error_message());
        if (!session_id()) session_start();
        $_SESSION['registration_errors'] = array($user_id->get_error_message());
        wp_redirect(home_url('/register'));
        exit;
    }

    // Success - log detailed information
    error_log('LN Reader Registration Success: User ID ' . $user_id . ' created for ' . $username);

    // Set user role explicitly
    $user = new WP_User($user_id);
    $user->set_role('subscriber');
    error_log('LN Reader Registration: Set role to subscriber for user ' . $user_id);

    // Auto login after registration
    wp_clear_auth_cookie();
    wp_set_current_user($user_id);
    wp_set_auth_cookie($user_id);

    error_log('LN Reader Registration: Auto-login completed for user ' . $user_id);

    wp_redirect(home_url('/dashboard'));
    exit;
}
add_action('template_redirect', 'ln_reader_handle_registration');

// Handle password reset request
function ln_reader_handle_password_reset() {
    if (isset($_POST['ln_reader_reset_nonce']) && wp_verify_nonce($_POST['ln_reader_reset_nonce'], 'ln_reader_reset')) {
        $email = sanitize_email($_POST['email']);

        if (empty($email)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Please enter your email address.'), home_url('/reset-password')));
            exit;
        }

        if (!is_email($email)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Please enter a valid email address.'), home_url('/reset-password')));
            exit;
        }

        $user = get_user_by('email', $email);
        if (!$user) {
            wp_redirect(add_query_arg('reset_error', urlencode('No user found with that email address.'), home_url('/reset-password')));
            exit;
        }

        // Generate reset key
        $key = get_password_reset_key($user);
        if (is_wp_error($key)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Unable to generate reset key.'), home_url('/reset-password')));
            exit;
        }

        // Send reset email
        $reset_url = add_query_arg(array(
            'action' => 'rp',
            'key' => $key,
            'login' => rawurlencode($user->user_login)
        ), home_url('/reset-password'));

        $subject = 'Password Reset Request';
        $message = "Hi {$user->display_name},\n\n";
        $message .= "You requested a password reset. Click the link below to reset your password:\n\n";
        $message .= $reset_url . "\n\n";
        $message .= "If you didn't request this, please ignore this email.\n\n";
        $message .= "Best regards,\n" . get_bloginfo('name');

        if (wp_mail($email, $subject, $message)) {
            wp_redirect(add_query_arg('reset_sent', '1', home_url('/reset-password')));
        } else {
            wp_redirect(add_query_arg('reset_error', urlencode('Unable to send reset email.'), home_url('/reset-password')));
        }
        exit;
    }
}
add_action('init', 'ln_reader_handle_password_reset');

// Handle new password submission
function ln_reader_handle_new_password() {
    if (isset($_POST['ln_reader_new_password_nonce']) && wp_verify_nonce($_POST['ln_reader_new_password_nonce'], 'ln_reader_new_password')) {
        $key = sanitize_text_field($_POST['key']);
        $login = sanitize_text_field($_POST['login']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];

        $user = check_password_reset_key($key, $login);
        if (is_wp_error($user)) {
            wp_redirect(add_query_arg('reset_error', urlencode('Invalid or expired reset link.'), home_url('/reset-password')));
            exit;
        }

        if (empty($password) || empty($confirm_password)) {
            $reset_url = add_query_arg(array('action' => 'rp', 'key' => $key, 'login' => rawurlencode($login)), home_url('/reset-password'));
            wp_redirect(add_query_arg('reset_error', urlencode('Please fill in all fields.'), $reset_url));
            exit;
        }

        if (strlen($password) < 6) {
            $reset_url = add_query_arg(array('action' => 'rp', 'key' => $key, 'login' => rawurlencode($login)), home_url('/reset-password'));
            wp_redirect(add_query_arg('reset_error', urlencode('Password must be at least 6 characters long.'), $reset_url));
            exit;
        }

        if ($password !== $confirm_password) {
            $reset_url = add_query_arg(array('action' => 'rp', 'key' => $key, 'login' => rawurlencode($login)), home_url('/reset-password'));
            wp_redirect(add_query_arg('reset_error', urlencode('Passwords do not match.'), $reset_url));
            exit;
        }

        // Reset password
        reset_password($user, $password);

        wp_redirect(add_query_arg('password_reset', '1', home_url('/login')));
        exit;
    }
}
add_action('init', 'ln_reader_handle_new_password');

// Handle password change from dashboard
function ln_reader_handle_password_change() {
    // Only process if we have the nonce and it's a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['ln_reader_password_change_nonce'])) {
        return;
    }

    // Verify nonce
    if (!wp_verify_nonce($_POST['ln_reader_password_change_nonce'], 'ln_reader_password_change')) {
        error_log('LN Reader Password Change: Nonce verification failed');
        return;
    }

    error_log('LN Reader Password Change: Function called. URI: ' . $_SERVER['REQUEST_URI'] . ', Method: ' . $_SERVER['REQUEST_METHOD']);

    // Check if user is logged in
    if (!is_user_logged_in()) {
        error_log('LN Reader Password Change: User not logged in');
        wp_redirect(home_url('/login'));
        exit;
    }

    $current_password = sanitize_text_field($_POST['current_password']);
    $new_password = $_POST['new_password']; // Don't sanitize passwords
    $confirm_password = $_POST['confirm_password']; // Don't sanitize passwords

    $errors = array();
    $user = wp_get_current_user();

    error_log('LN Reader Password Change: Processing for user ID: ' . $user->ID . ' (' . $user->user_login . ')');

    // Validation
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $errors[] = 'Please fill in all fields.';
        error_log('LN Reader Password Change: Empty fields validation failed');
    }

    if (!empty($current_password) && !wp_check_password($current_password, $user->user_pass, $user->ID)) {
        $errors[] = 'Current password is incorrect.';
        error_log('LN Reader Password Change: Current password validation failed');
    }

    if (strlen($new_password) < 6) {
        $errors[] = 'New password must be at least 6 characters long.';
        error_log('LN Reader Password Change: Password length validation failed');
    }

    if ($new_password !== $confirm_password) {
        $errors[] = 'New passwords do not match.';
        error_log('LN Reader Password Change: Password confirmation validation failed');
    }

    if (!empty($errors)) {
        error_log('LN Reader Password Change: Validation errors: ' . implode(', ', $errors));
        if (!session_id()) session_start();
        $_SESSION['password_change_errors'] = $errors;
        wp_redirect(home_url('/dashboard#password'));
        exit;
    }

    // Update password
    error_log('LN Reader Password Change: Updating password for user ID: ' . $user->ID);

    // Store original hash for comparison
    $original_hash = $user->user_pass;
    error_log('LN Reader Password Change: Original hash: ' . substr($original_hash, 0, 20) . '...');

    // Update the password
    wp_set_password($new_password, $user->ID);

    // Verify password was updated by getting fresh user data
    wp_cache_delete($user->ID, 'users');
    $updated_user = get_user_by('id', $user->ID);
    $new_hash = $updated_user->user_pass;

    error_log('LN Reader Password Change: New hash: ' . substr($new_hash, 0, 20) . '...');
    error_log('LN Reader Password Change: Hash changed: ' . ($original_hash !== $new_hash ? 'YES' : 'NO'));

    // Test new password
    $new_password_works = wp_check_password($new_password, $new_hash, $user->ID);
    $old_password_fails = !wp_check_password($current_password, $new_hash, $user->ID);

    error_log('LN Reader Password Change: New password works: ' . ($new_password_works ? 'YES' : 'NO'));
    error_log('LN Reader Password Change: Old password fails: ' . ($old_password_fails ? 'YES' : 'NO'));

    if ($new_password_works && $old_password_fails) {
        error_log('LN Reader Password Change: Password update verified successfully');

        // Re-authenticate user with new password
        wp_clear_auth_cookie();
        wp_set_current_user($user->ID);
        wp_set_auth_cookie($user->ID);

        error_log('LN Reader Password Change: User re-authenticated successfully');

        wp_redirect(add_query_arg('password_changed', '1', home_url('/dashboard#password')));
        exit;
    } else {
        error_log('LN Reader Password Change: Password update verification failed');
        if (!session_id()) session_start();
        $_SESSION['password_change_errors'] = array('Password update failed. Please try again.');
        wp_redirect(home_url('/dashboard#password'));
        exit;
    }
}
add_action('init', 'ln_reader_handle_password_change', 1);

// Debug function to track password change attempts
function ln_reader_debug_password_change_tracking() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ln_reader_password_change_nonce'])) {
        error_log('LN Reader Debug: Password change form submitted. URI: ' . $_SERVER['REQUEST_URI']);
        error_log('LN Reader Debug: POST keys: ' . implode(', ', array_keys($_POST)));
        error_log('LN Reader Debug: User logged in: ' . (is_user_logged_in() ? 'YES' : 'NO'));
        if (is_user_logged_in()) {
            $user = wp_get_current_user();
            error_log('LN Reader Debug: Current user: ' . $user->user_login . ' (ID: ' . $user->ID . ')');
        }
    }
}
add_action('init', 'ln_reader_debug_password_change_tracking', 0);

// Handle profile update from dashboard
function ln_reader_handle_profile_update() {
    if (isset($_POST['ln_reader_profile_update_nonce']) && wp_verify_nonce($_POST['ln_reader_profile_update_nonce'], 'ln_reader_profile_update')) {
        if (!is_user_logged_in()) {
            wp_redirect(home_url('/login'));
            exit;
        }

        $display_name = sanitize_text_field($_POST['display_name']);
        $email = sanitize_email($_POST['email']);
        $bio = sanitize_textarea_field($_POST['bio']);

        $errors = array();
        $user_id = get_current_user_id();

        // Validation
        if (empty($display_name)) {
            $errors[] = 'Display name is required.';
        }

        if (empty($email) || !is_email($email)) {
            $errors[] = 'Please enter a valid email address.';
        }

        // Check if email is already used by another user
        $existing_user = get_user_by('email', $email);
        if ($existing_user && $existing_user->ID !== $user_id) {
            $errors[] = 'Email is already in use by another user.';
        }

        if (!empty($errors)) {
            if (!session_id()) session_start();
            $_SESSION['profile_update_errors'] = $errors;
            wp_redirect(home_url('/dashboard#profile'));
            exit;
        }

        // Update user data
        $user_data = array(
            'ID' => $user_id,
            'display_name' => $display_name,
            'user_email' => $email
        );

        $result = wp_update_user($user_data);

        if (is_wp_error($result)) {
            if (!session_id()) session_start();
            $_SESSION['profile_update_errors'] = array($result->get_error_message());
            wp_redirect(home_url('/dashboard#profile'));
            exit;
        }

        // Update bio
        update_user_meta($user_id, 'description', $bio);

        wp_redirect(add_query_arg('profile_updated', '1', home_url('/dashboard#profile')));
        exit;
    }
}
add_action('template_redirect', 'ln_reader_handle_profile_update');

// Reading history functions
function ln_reader_add_to_reading_history($user_id, $novel_id, $chapter_id) {
    $history = get_user_meta($user_id, 'reading_history', true);
    if (!is_array($history)) {
        $history = array();
    }

    // Remove existing entry for this novel
    foreach ($history as $key => $item) {
        if ($item['novel_id'] == $novel_id) {
            unset($history[$key]);
            break;
        }
    }

    // Add new entry at the beginning
    array_unshift($history, array(
        'novel_id' => $novel_id,
        'chapter_id' => $chapter_id,
        'timestamp' => current_time('timestamp')
    ));

    // Keep only last 50 entries
    $history = array_slice($history, 0, 50);

    update_user_meta($user_id, 'reading_history', $history);
}

function ln_reader_get_reading_history($user_id, $limit = 10) {
    $history = get_user_meta($user_id, 'reading_history', true);
    if (!is_array($history)) {
        return array();
    }

    $result = array();
    $count = 0;

    foreach ($history as $item) {
        if ($count >= $limit) break;

        $novel = get_post($item['novel_id']);
        $chapter = get_post($item['chapter_id']);

        if ($novel && $chapter) {
            $result[] = array(
                'novel' => $novel,
                'chapter' => $chapter,
                'timestamp' => $item['timestamp']
            );
            $count++;
        }
    }

    return $result;
}

// Auto-add to reading history when viewing a chapter
function ln_reader_auto_add_reading_history() {
    if (is_user_logged_in() && is_single()) {
        $post_id = get_the_ID();
        $novel_id = get_post_meta($post_id, '_novel_id', true);

        if ($novel_id) {
            $user_id = get_current_user_id();
            ln_reader_add_to_reading_history($user_id, $novel_id, $post_id);
        }
    }
}
add_action('wp_head', 'ln_reader_auto_add_reading_history');

// Handle bookmark removal from dashboard
function ln_reader_handle_bookmark_removal() {
    if (isset($_POST['ln_reader_remove_bookmark_nonce']) && wp_verify_nonce($_POST['ln_reader_remove_bookmark_nonce'], 'ln_reader_remove_bookmark')) {
        if (!is_user_logged_in()) {
            wp_redirect(home_url('/login'));
            exit;
        }

        $novel_id = intval($_POST['novel_id']);
        $user_id = get_current_user_id();

        $bookmarks = get_user_meta($user_id, 'bookmarked_novels', true);
        if (!is_array($bookmarks)) {
            $bookmarks = array();
        }

        // Remove from bookmarks
        $key = array_search($novel_id, $bookmarks);
        if ($key !== false) {
            unset($bookmarks[$key]);
            $bookmarks = array_values($bookmarks); // Re-index array
            update_user_meta($user_id, 'bookmarked_novels', $bookmarks);
        }

        wp_redirect(home_url('/dashboard#bookmarks'));
        exit;
    }
}
add_action('init', 'ln_reader_handle_bookmark_removal');

// Enqueue authentication styles and scripts
function ln_reader_enqueue_auth_assets() {
    // Enqueue auth CSS on authentication pages
    if (is_page(array('login', 'register', 'reset-password', 'dashboard'))) {
        wp_enqueue_style('ln-reader-auth', get_template_directory_uri() . '/css/auth.css', array(), '1.0.0');
        wp_enqueue_script('ln-reader-auth', get_template_directory_uri() . '/js/auth.js', array('jquery'), '1.0.0', true);

        // Add nonce for AJAX requests
        wp_localize_script('ln-reader-auth', 'ln_reader_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('ln_reader_nonce'),
            'bookmark_nonce' => wp_create_nonce('bookmark_nonce')
        ));
    }
}
add_action('wp_enqueue_scripts', 'ln_reader_enqueue_auth_assets');

// Redirect wp-login.php to custom login page
function ln_reader_redirect_login_page() {
    $page_viewed = basename($_SERVER['REQUEST_URI']);

    if ($page_viewed == "wp-login.php" && $_SERVER['REQUEST_METHOD'] == 'GET') {
        $redirect_to = isset($_REQUEST['redirect_to']) ? $_REQUEST['redirect_to'] : null;

        if (isset($_REQUEST['action'])) {
            $action = $_REQUEST['action'];

            if ($action == 'register') {
                wp_redirect(home_url('/register'));
                exit;
            } elseif ($action == 'lostpassword' || $action == 'rp') {
                wp_redirect(home_url('/reset-password'));
                exit;
            }
        }

        if ($redirect_to) {
            wp_redirect(add_query_arg('redirect_to', urlencode($redirect_to), home_url('/login')));
        } else {
            wp_redirect(home_url('/login'));
        }
        exit;
    }
}
add_action('init', 'ln_reader_redirect_login_page');

// Disable WordPress default login/register URLs for non-admins
function ln_reader_disable_default_auth_pages() {
    global $pagenow;

    if ($pagenow === 'wp-login.php' && $_SERVER['REQUEST_METHOD'] == 'POST') {
        // Allow POST requests to wp-login.php for logout and other necessary functions
        return;
    }

    if ($pagenow === 'wp-login.php' && !current_user_can('administrator')) {
        ln_reader_redirect_login_page();
    }
}
add_action('init', 'ln_reader_disable_default_auth_pages');

// Start session di WordPress
function start_session() {
    if (!session_id()) {
        session_start();
    }
}
add_action('init', 'start_session', 1);

// Hook ke wp_head untuk menghitung views
add_action('wp_head', 'set_post_views');

// Fungsi untuk mendapatkan jumlah views
function get_post_views($post_id) {
    $count = get_post_meta($post_id, 'post_views', true);
    
    if ($count == '') {
        delete_post_meta($post_id, 'post_views');
        add_post_meta($post_id, 'post_views', '0');
        return "0";
    }
    
    return $count;
}

// Tambahkan custom image sizes untuk novel thumbnails
function add_novel_image_sizes() {
    add_image_size('novel-featured', 800, 400, true);  // For featured novel
    add_image_size('novel-card', 300, 400, true);      // For novel cards
    add_image_size('novel-thumb', 60, 80, true);       // For popular novels list
}
add_action('after_setup_theme', 'add_novel_image_sizes');

// Tambahkan meta box untuk featured novel
function add_featured_novel_meta_box() {
    add_meta_box(
        'featured_novel_meta',
        'Featured Novel',
        'featured_novel_meta_box_html',
        'novel',
        'side',
        'high'
    );
}
add_action('add_meta_boxes', 'add_featured_novel_meta_box');

// HTML untuk meta box featured novel
function featured_novel_meta_box_html($post) {
    $is_featured = get_post_meta($post->ID, '_is_featured', true);
    ?>
    <label>
        <input type="checkbox" name="is_featured" value="1" <?php checked($is_featured, '1'); ?>>
        Set as Featured Novel
    </label>
    <?php
}

// Save featured novel meta
function save_featured_novel_meta($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;
    
    // Update featured status
    $is_featured = isset($_POST['is_featured']) ? '1' : '0';
    update_post_meta($post_id, '_is_featured', $is_featured);
    
    // If this novel is being set as featured, unset any other featured novels
    if ($is_featured === '1') {
        $args = array(
            'post_type' => 'novel',
            'posts_per_page' => -1,
            'meta_key' => '_is_featured',
            'meta_value' => '1',
            'post__not_in' => array($post_id)
        );
        
        $featured_novels = get_posts($args);
        foreach ($featured_novels as $novel) {
            update_post_meta($novel->ID, '_is_featured', '0');
        }
    }
}
add_action('save_post_novel', 'save_featured_novel_meta');

// Fungsi helper untuk mendapatkan last chapter dari novel
function get_novel_last_chapter($novel_id) {
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 1,
        'meta_key' => '_novel_id',
        'meta_value' => $novel_id,
        'orderby' => 'meta_value_num',
        'order' => 'DESC',
        'meta_query' => array(
            array(
                'key' => '_chapter_number',
                'type' => 'NUMERIC'
            )
        )
    );
    
    $last_chapter = get_posts($args);
    return !empty($last_chapter) ? $last_chapter[0] : null;
}

// Filter novels in archive page
function filter_novel_archive($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('novel')) {
        // Sort order
        $orderby = isset($_GET['orderby']) ? $_GET['orderby'] : 'date';
        switch ($orderby) {
            case 'title':
                $query->set('orderby', 'title');
                $query->set('order', 'ASC');
                break;
            case 'rating':
                $query->set('meta_key', '_average_rating');
                $query->set('orderby', 'meta_value_num');
                $query->set('order', 'DESC');
                break;
            default: // date
                $query->set('orderby', 'modified');
                $query->set('order', 'DESC');
                break;
        }

        // Time period filter
        $time = isset($_GET['time']) ? $_GET['time'] : 'all';
        if ($time !== 'all') {
            $date_query = array();
            switch ($time) {
                case 'today':
                    $date_query[] = array('after' => '1 day ago');
                    break;
                case 'week':
                    $date_query[] = array('after' => '1 week ago');
                    break;
                case 'month':
                    $date_query[] = array('after' => '1 month ago');
                    break;
            }
            $query->set('date_query', $date_query);
        }

        // Rating filter
        if (isset($_GET['rating']) && !empty($_GET['rating'])) {
            $rating = intval($_GET['rating']);
            $meta_query = array(
                array(
                    'key' => '_average_rating',
                    'value' => $rating,
                    'compare' => '>=',
                    'type' => 'NUMERIC'
                )
            );
            $query->set('meta_query', $meta_query);
        }

        // Set posts per page
        $query->set('posts_per_page', 12);
    }
}
add_action('pre_get_posts', 'filter_novel_archive');

// Add custom feed for latest chapters
function add_latest_chapters_feed() {
    add_feed('latest-chapters', 'generate_latest_chapters_feed');
}
add_action('init', 'add_latest_chapters_feed');

// Generate feed content for latest chapters
function generate_latest_chapters_feed() {
    header('Content-Type: application/rss+xml; charset=UTF-8');

    // Get latest 20 chapters
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 20,
        'orderby' => 'date',
        'order' => 'DESC',
        'meta_query' => array(
            array(
                'key' => '_novel_id',
                'compare' => 'EXISTS'
            )
        )
    );

    $posts = get_posts($args);

    echo '<?xml version="1.0" encoding="UTF-8"?>';
    ?>
    <rss version="2.0"
        xmlns:content="http://purl.org/rss/1.0/modules/content/"
        xmlns:dc="http://purl.org/dc/elements/1.1/"
        xmlns:atom="http://www.w3.org/2005/Atom">
        <channel>
            <title><?php bloginfo_rss('name'); ?> - Latest Chapters</title>
            <link><?php bloginfo_rss('url'); ?></link>
            <description>Latest chapters from all novels</description>
            <language><?php bloginfo_rss('language'); ?></language>
            <lastBuildDate><?php echo mysql2date('D, d M Y H:i:s +0000', get_lastpostmodified('GMT'), false); ?></lastBuildDate>
            <atom:link href="<?php self_link(); ?>" rel="self" type="application/rss+xml" />
            <?php
            foreach ($posts as $post) {
                setup_postdata($post);
                $novel_id = get_post_meta($post->ID, '_novel_id', true);
                $novel = get_post($novel_id);
                $chapter_number = get_post_meta($post->ID, '_chapter_number', true);
                ?>
                <item>
                    <title><?php echo esc_html($novel->post_title . ' - Chapter ' . $chapter_number); ?></title>
                    <link><?php echo esc_url(get_permalink($post->ID)); ?></link>
                    <guid isPermaLink="false"><?php echo esc_url(get_permalink($post->ID)); ?></guid>
                    <pubDate><?php echo mysql2date('D, d M Y H:i:s +0000', get_post_time('Y-m-d H:i:s', true, $post), false); ?></pubDate>
                    <description><![CDATA[<?php
                        echo 'Novel: ' . esc_html($novel->post_title) . "\n";
                        echo 'Chapter: ' . esc_html($chapter_number) . "\n";
                        echo 'Posted: ' . get_the_date('', $post->ID) . "\n";
                        echo 'Link: ' . get_permalink($post->ID);
                    ?>]]></description>
                </item>
                <?php
            }
            wp_reset_postdata();
            ?>
        </channel>
    </rss>
    <?php
}

// Modify the main RSS feed to show novel chapters
function modify_main_feed_query($query) {
    if ($query->is_feed() && $query->is_main_query()) {
        // Only show posts that are chapters (have _novel_id meta)
        $query->set('meta_query', array(
            array(
                'key' => '_novel_id',
                'compare' => 'EXISTS'
            )
        ));
        $query->set('posts_per_page', 20);
    }
}
add_action('pre_get_posts', 'modify_main_feed_query');

// Customize feed title to show novel and chapter
function customize_feed_title($title) {
    if (is_feed()) {
        global $post;
        $novel_id = get_post_meta($post->ID, '_novel_id', true);
        $chapter_number = get_post_meta($post->ID, '_chapter_number', true);

        if ($novel_id && $chapter_number) {
            $novel = get_post($novel_id);
            if ($novel) {
                $title = $novel->post_title . ' Chapter ' . $chapter_number;
            }
        }
    }
    return $title;
}
add_filter('the_title_rss', 'customize_feed_title');

// Customize the main feed content to show novel chapter format
function customize_feed_content($content) {
    if (is_feed()) {
        global $post;
        $novel_id = get_post_meta($post->ID, '_novel_id', true);
        $chapter_number = get_post_meta($post->ID, '_chapter_number', true);

        if ($novel_id && $chapter_number) {
            $novel = get_post($novel_id);
            if ($novel) {
                $content = 'Novel: ' . esc_html($novel->post_title) . "\n";
                $content .= 'Chapter: ' . esc_html($chapter_number) . "\n";
                $content .= 'Posted: ' . get_the_date('', $post->ID) . "\n";
                $content .= 'Link: ' . get_permalink($post->ID);
            }
        }
    }
    return $content;
}
add_filter('the_excerpt_rss', 'customize_feed_content');
add_filter('the_content_feed', 'customize_feed_content');

// Flush rewrite rules when theme is activated to make the feed work
function flush_feed_rewrite_rules() {
    add_latest_chapters_feed();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'flush_feed_rewrite_rules');

// Force flush rewrite rules for feed
function force_flush_feed_rules() {
    if (isset($_GET['flush_feed'])) {
        flush_rewrite_rules();
        wp_redirect(home_url('/feed/'));
        exit;
    }
}
add_action('init', 'force_flush_feed_rules');

// Set default site description if empty
function set_default_site_description() {
    $description = get_option('blogdescription');
    if (empty($description)) {
        update_option('blogdescription', 'Latest light novel translations and updates');
    }
}
add_action('init', 'set_default_site_description');
