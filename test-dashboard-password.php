<?php
/**
 * Test Dashboard Password Change
 * This script simulates the exact dashboard password change process
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Dashboard Password Change Test</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="password"] { 
        width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
    }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
    .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
</style>';

// Handle password change form submission (simulating dashboard form)
if (isset($_POST['ln_reader_password_change_nonce']) && wp_verify_nonce($_POST['ln_reader_password_change_nonce'], 'ln_reader_password_change')) {
    echo '<div class="alert alert-info">';
    echo '<h3>Processing Password Change (Simulating Dashboard)...</h3>';
    
    if (!is_user_logged_in()) {
        echo '<p class="error">User not logged in!</p>';
    } else {
        $current_user = wp_get_current_user();
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];
        
        echo "<p><strong>User:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
        echo "<p><strong>Processing password change...</strong></p>";
        
        $errors = array();
        
        // Validation (same as in the function)
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $errors[] = 'Please fill in all fields.';
        }
        
        if (!empty($current_password) && !wp_check_password($current_password, $current_user->user_pass, $current_user->ID)) {
            $errors[] = 'Current password is incorrect.';
        }
        
        if (strlen($new_password) < 6) {
            $errors[] = 'New password must be at least 6 characters long.';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'New passwords do not match.';
        }
        
        if (!empty($errors)) {
            echo '<div class="alert alert-danger">';
            echo '<h4>Validation Errors:</h4>';
            echo '<ul>';
            foreach ($errors as $error) {
                echo '<li>' . esc_html($error) . '</li>';
            }
            echo '</ul>';
            echo '</div>';
        } else {
            // Update password (same as in the function)
            echo '<p>Updating password...</p>';
            wp_set_password($new_password, $current_user->ID);
            
            // Verify password was updated
            $updated_user = get_user_by('id', $current_user->ID);
            $new_valid = wp_check_password($new_password, $updated_user->user_pass, $current_user->ID);
            $old_invalid = !wp_check_password($current_password, $updated_user->user_pass, $current_user->ID);
            
            echo '<div class="alert alert-success">';
            echo '<h4>Password Update Results:</h4>';
            echo '<ul>';
            echo '<li>New password valid: ' . ($new_valid ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . '</li>';
            echo '<li>Old password invalid: ' . ($old_invalid ? '<span class="success">Yes</span>' : '<span class="error">No</span>') . '</li>';
            echo '</ul>';
            
            if ($new_valid && $old_invalid) {
                echo '<p class="success">✓ Password change successful!</p>';
                
                // Re-authenticate user (same as in the function)
                wp_clear_auth_cookie();
                wp_set_current_user($current_user->ID);
                wp_set_auth_cookie($current_user->ID);
                
                echo '<p class="info">User re-authenticated successfully.</p>';
            } else {
                echo '<p class="error">✗ Password change failed!</p>';
            }
            echo '</div>';
        }
    }
    echo '</div>';
}

// Handle test user creation
if (isset($_POST['create_test_user'])) {
    $test_username = 'dashtest_' . time();
    $test_email = 'dashtest_' . time() . '@example.com';
    $test_password = 'TestPass123';
    
    $user_id = wp_create_user($test_username, $test_password, $test_email);
    
    if (!is_wp_error($user_id)) {
        wp_clear_auth_cookie();
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        echo '<div class="alert alert-success">';
        echo '<p>Test user created and logged in:</p>';
        echo '<ul>';
        echo '<li><strong>Username:</strong> ' . $test_username . '</li>';
        echo '<li><strong>Password:</strong> ' . $test_password . '</li>';
        echo '<li><strong>User ID:</strong> ' . $user_id . '</li>';
        echo '</ul>';
        echo '</div>';
    }
}

echo '<h2>Current Status</h2>';
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo "<p><strong>Logged in as:</strong> {$current_user->user_login} (ID: {$current_user->ID})</p>";
} else {
    echo "<p class='error'>No user is logged in.</p>";
    echo '<form method="post">';
    echo '<button type="submit" name="create_test_user" class="btn">Create Test User & Login</button>';
    echo '</form>';
}

if (is_user_logged_in()) {
    echo '<h2>Test Password Change (Dashboard Simulation)</h2>';
    echo '<p>This form uses the exact same nonce and field names as the dashboard:</p>';
    
    echo '<form method="post">';
    wp_nonce_field('ln_reader_password_change', 'ln_reader_password_change_nonce');
    
    echo '<div class="form-group">';
    echo '<label for="current_password">Current Password:</label>';
    echo '<input type="password" id="current_password" name="current_password" placeholder="Enter current password" required>';
    echo '</div>';
    
    echo '<div class="form-group">';
    echo '<label for="new_password">New Password:</label>';
    echo '<input type="password" id="new_password" name="new_password" placeholder="Enter new password (min 6 chars)" required>';
    echo '</div>';
    
    echo '<div class="form-group">';
    echo '<label for="confirm_password">Confirm New Password:</label>';
    echo '<input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm new password" required>';
    echo '</div>';
    
    echo '<button type="submit" class="btn">Change Password</button>';
    echo '</form>';
}

echo '<h2>Function Status</h2>';
$function_exists = function_exists('ln_reader_handle_password_change');
echo "<p><strong>Password change function exists:</strong> " . 
     ($function_exists ? '<span class="success">✓ Yes</span>' : '<span class="error">✗ No</span>') . "</p>";

if ($function_exists) {
    $hooked = has_action('template_redirect', 'ln_reader_handle_password_change');
    echo "<p><strong>Function hooked to 'template_redirect':</strong> " . 
         ($hooked ? '<span class="success">✓ Yes</span>' : '<span class="error">✗ No</span>') . "</p>";
}

echo '<h2>Links</h2>';
echo '<ul>';
echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Visit Actual Dashboard</a></li>';
echo '<li><a href="' . home_url('/dashboard#password') . '" target="_blank">Visit Dashboard Password Tab</a></li>';
echo '<li><a href="test-password-change.php">Run Password Change Test</a></li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Test completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
