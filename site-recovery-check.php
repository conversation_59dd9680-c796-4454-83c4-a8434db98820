<?php
/**
 * Site Recovery Verification Script
 * 
 * This script checks if your site is back online after the emergency fix.
 * Upload this file to your theme directory and run it:
 * http://localhost/epic/wp-content/themes/lnreader/site-recovery-check.php
 */

// Include WordPress
require_once('../../../wp-load.php');

echo '<h1>LN Reader Site Recovery Check</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .status-box { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
</style>';

echo '<div class="status-box">';
echo '<h2>Recovery Status Check</h2>';

// Test 1: Basic WordPress functionality
echo '<h3>1. WordPress Core Functionality</h3>';
try {
    $site_url = home_url();
    $admin_url = admin_url();
    echo '<p class="success">✓ WordPress is loading properly</p>';
    echo '<p>Site URL: <a href="' . $site_url . '" target="_blank">' . $site_url . '</a></p>';
    echo '<p>Admin URL: <a href="' . $admin_url . '" target="_blank">' . $admin_url . '</a></p>';
} catch (Exception $e) {
    echo '<p class="error">✗ WordPress core issue: ' . $e->getMessage() . '</p>';
}

// Test 2: Database connectivity
echo '<h3>2. Database Connectivity</h3>';
try {
    $posts = get_posts(array('numberposts' => 1));
    echo '<p class="success">✓ Database is accessible</p>';
} catch (Exception $e) {
    echo '<p class="error">✗ Database issue: ' . $e->getMessage() . '</p>';
}

// Test 3: Theme functionality
echo '<h3>3. Theme Status</h3>';
try {
    $theme = wp_get_theme();
    echo '<p class="success">✓ Theme is active: ' . $theme->get('Name') . '</p>';
    echo '<p>Theme directory: ' . get_template_directory() . '</p>';
} catch (Exception $e) {
    echo '<p class="error">✗ Theme issue: ' . $e->getMessage() . '</p>';
}

// Test 4: Functions.php status
echo '<h3>4. Functions.php Status</h3>';
$functions_file = get_template_directory() . '/functions.php';
if (file_exists($functions_file)) {
    $file_size = filesize($functions_file);
    $last_modified = date('Y-m-d H:i:s', filemtime($functions_file));
    echo '<p class="success">✓ functions.php exists</p>';
    echo '<p>File size: ' . $file_size . ' bytes</p>';
    echo '<p>Last modified: ' . $last_modified . '</p>';
    
    // Check for authentication system
    $content = file_get_contents($functions_file);
    if (strpos($content, 'temporarily disabled') !== false) {
        echo '<p class="warning">⚠ Authentication system is temporarily disabled (this is expected)</p>';
    } else {
        echo '<p class="info">ℹ Authentication system status unclear</p>';
    }
} else {
    echo '<p class="error">✗ functions.php not found</p>';
}

// Test 5: User access
echo '<h3>5. User Access Test</h3>';
if (is_user_logged_in()) {
    $current_user = wp_get_current_user();
    echo '<p class="success">✓ You are logged in as: ' . $current_user->user_login . '</p>';
    if (current_user_can('administrator')) {
        echo '<p class="success">✓ You have administrator privileges</p>';
    } else {
        echo '<p class="info">ℹ You are logged in as a regular user</p>';
    }
} else {
    echo '<p class="info">ℹ You are not logged in (this is normal for this test)</p>';
}

// Test 6: Error checking
echo '<h3>6. Error Log Check</h3>';
if (defined('WP_DEBUG') && WP_DEBUG) {
    echo '<p class="info">ℹ WordPress debug mode is enabled</p>';
    
    $debug_log = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($debug_log)) {
        $log_size = filesize($debug_log);
        $log_modified = date('Y-m-d H:i:s', filemtime($debug_log));
        echo '<p>Debug log exists (Size: ' . $log_size . ' bytes, Modified: ' . $log_modified . ')</p>';
        
        // Check for recent errors
        $recent_errors = shell_exec("tail -20 " . escapeshellarg($debug_log));
        if ($recent_errors && strpos($recent_errors, 'Fatal error') !== false) {
            echo '<p class="warning">⚠ Recent fatal errors found in debug log</p>';
        } else {
            echo '<p class="success">✓ No recent fatal errors in debug log</p>';
        }
    } else {
        echo '<p class="success">✓ No debug log file (no errors)</p>';
    }
} else {
    echo '<p class="info">ℹ WordPress debug mode is disabled</p>';
}

echo '</div>';

// Test 7: Site accessibility test
echo '<div class="status-box">';
echo '<h2>Site Accessibility Test</h2>';

$test_urls = array(
    'Homepage' => home_url(),
    'Admin' => admin_url(),
    'Login' => wp_login_url()
);

foreach ($test_urls as $name => $url) {
    echo '<h4>' . $name . '</h4>';
    
    $response = wp_remote_get($url, array(
        'timeout' => 10,
        'sslverify' => false
    ));
    
    if (is_wp_error($response)) {
        echo '<p class="error">✗ Failed to access: ' . $response->get_error_message() . '</p>';
    } else {
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code === 200) {
            echo '<p class="success">✓ Accessible (HTTP ' . $response_code . ')</p>';
        } elseif ($response_code === 302 || $response_code === 301) {
            echo '<p class="info">ℹ Redirecting (HTTP ' . $response_code . ')</p>';
        } else {
            echo '<p class="warning">⚠ HTTP ' . $response_code . '</p>';
        }
    }
    
    echo '<p>URL: <a href="' . $url . '" target="_blank">' . $url . '</a></p>';
}

echo '</div>';

// Recovery summary
echo '<div class="status-box">';
echo '<h2>Recovery Summary</h2>';

$site_response = wp_remote_get(home_url(), array('timeout' => 10));
$is_site_accessible = !is_wp_error($site_response) && wp_remote_retrieve_response_code($site_response) === 200;

if ($is_site_accessible) {
    echo '<p class="success"><strong>✓ RECOVERY SUCCESSFUL!</strong></p>';
    echo '<p>Your site is now accessible and the 503 error has been resolved.</p>';
} else {
    echo '<p class="error"><strong>✗ RECOVERY INCOMPLETE</strong></p>';
    echo '<p>Your site may still have issues. Please check the details above.</p>';
}

echo '<h3>What was fixed:</h3>';
echo '<ul>';
echo '<li>Removed conflicting authentication functions</li>';
echo '<li>Disabled problematic login redirects</li>';
echo '<li>Removed duplicate session handlers</li>';
echo '<li>Restored basic WordPress functionality</li>';
echo '</ul>';

echo '<h3>Current status:</h3>';
echo '<ul>';
echo '<li class="warning">Authentication system is temporarily disabled</li>';
echo '<li class="info">Users must use standard WordPress login (wp-login.php)</li>';
echo '<li class="info">Custom pages (login, register, dashboard) are not functional</li>';
echo '<li class="success">Core WordPress functionality is restored</li>';
echo '</ul>';

echo '<h3>Next steps:</h3>';
echo '<ol>';
echo '<li><strong>Test your site:</strong> <a href="' . home_url() . '" target="_blank">Visit homepage</a></li>';
echo '<li><strong>Test admin access:</strong> <a href="' . admin_url() . '" target="_blank">Visit admin</a></li>';
echo '<li><strong>Delete recovery scripts:</strong> Remove this file and emergency-fix.php</li>';
echo '<li><strong>Plan authentication re-implementation:</strong> Contact support for proper implementation</li>';
echo '</ol>';

echo '</div>';

echo '<hr>';
echo '<p><strong>Important:</strong> Delete this file after verification for security reasons.</p>';
echo '<p><em>Recovery check completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
