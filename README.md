# LN Reader WordPress Theme

A modern WordPress theme designed for light novel reading websites. This theme provides a clean, user-friendly interface with features specifically tailored for managing and reading light novels.

## Features

- Modern, responsive design
- Custom post types for novels and chapters
- Chapter navigation with keyboard support
- Reading progress tracking
- Bookmarking system
- Popular novels tracking
- Latest updates section
- Content protection options
- Dark/Light theme switcher
- Reading time estimation
- Search functionality with autocomplete
- Infinite scroll for novel listings

## Installation

1. Download the theme files
2. Upload the theme folder to your WordPress installation's `wp-content/themes` directory
3. Activate the theme through the WordPress admin panel (Appearance > Themes)

## Required Plugins

- Advanced Custom Fields (ACF) - for novel and chapter metadata
- Classic Editor - recommended for chapter content editing

## Configuration

### Setting Up Custom Post Types

The theme automatically registers two custom post types:
- Novels
- Chapters

### Theme Options

Navigate to Appearance > Customize to configure:
- Site logo
- Social media links
- Content protection settings
- Color scheme
- Typography options

### Creating Content

1. Add a New Novel:
   - Go to Novels > Add New
   - Fill in the title, description, and featured image
   - Set the author and status in the Novel Details meta box
   - Publish the novel

2. Add Chapters:
   - Go to Chapters > Add New
   - Select the parent novel
   - Enter the chapter number
   - Add the chapter content
   - Publish the chapter

## Development

### File Structure

```
ln-reader/
├── style.css
├── functions.php
├── index.php
├── header.php
├── footer.php
├── single-novel.php
├── single-chapter.php
├── js/
│   └── main.js
└── images/
    └── default-cover.jpg
```

### Customization

The theme uses Bootstrap 4 for its grid system and components. You can customize the appearance by:

1. Modifying `style.css`
2. Adding custom JavaScript to `js/main.js`
3. Creating child theme for major modifications

## License

This theme is licensed under the GPL v2 or later.

## Support

For support and feature requests, please create an issue in the GitHub repository.
