<?php
/**
 * Test script for LN Reader Authentication System
 * 
 * This script tests various aspects of the authentication system.
 * Upload this file to your theme directory and run it by visiting:
 * http://localhost/epic/wp-content/themes/lnreader/test-auth-system.php
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('administrator')) {
    die('Access denied. You must be an administrator to run this script.');
}

echo '<h1>LN Reader Authentication System Test</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .test-section { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #007cba; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>';

// Test 1: Check if custom pages exist
echo '<div class="test-section">';
echo '<h2>Test 1: Custom Pages Existence</h2>';

$pages_to_check = array('login', 'register', 'dashboard', 'reset-password');
$pages_status = array();

echo '<table>';
echo '<tr><th>Page</th><th>Status</th><th>ID</th><th>Template</th><th>URL</th><th>Action</th></tr>';

foreach ($pages_to_check as $slug) {
    $page = get_page_by_path($slug);
    if ($page) {
        $template = get_post_meta($page->ID, '_wp_page_template', true);
        $url = get_permalink($page->ID);
        $status = '<span class="success">EXISTS</span>';
        $action = '<a href="' . $url . '" target="_blank">Visit</a>';
        $pages_status[$slug] = true;
    } else {
        $status = '<span class="error">MISSING</span>';
        $template = 'N/A';
        $url = 'N/A';
        $action = '<a href="?create_page=' . $slug . '">Create</a>';
        $pages_status[$slug] = false;
    }
    
    echo "<tr>";
    echo "<td>{$slug}</td>";
    echo "<td>{$status}</td>";
    echo "<td>" . ($page ? $page->ID : 'N/A') . "</td>";
    echo "<td>{$template}</td>";
    echo "<td>{$url}</td>";
    echo "<td>{$action}</td>";
    echo "</tr>";
}

echo '</table>';
echo '</div>';

// Test 2: Check template files
echo '<div class="test-section">';
echo '<h2>Test 2: Template Files</h2>';

$theme_dir = get_template_directory();
$required_templates = array(
    'page-login.php',
    'page-register.php', 
    'page-dashboard.php',
    'page-reset-password.php'
);

echo '<table>';
echo '<tr><th>Template File</th><th>Status</th><th>Size</th><th>Modified</th></tr>';

foreach ($required_templates as $template) {
    $template_path = $theme_dir . '/' . $template;
    if (file_exists($template_path)) {
        $size = filesize($template_path);
        $modified = date('Y-m-d H:i:s', filemtime($template_path));
        $status = '<span class="success">EXISTS</span>';
    } else {
        $size = 'N/A';
        $modified = 'N/A';
        $status = '<span class="error">MISSING</span>';
    }
    
    echo "<tr>";
    echo "<td>{$template}</td>";
    echo "<td>{$status}</td>";
    echo "<td>{$size} bytes</td>";
    echo "<td>{$modified}</td>";
    echo "</tr>";
}

echo '</table>';
echo '</div>';

// Test 3: WordPress Settings
echo '<div class="test-section">';
echo '<h2>Test 3: WordPress Configuration</h2>';

echo '<table>';
echo '<tr><th>Setting</th><th>Current Value</th><th>Expected</th><th>Status</th></tr>';

$settings_to_check = array(
    'users_can_register' => array('expected' => '1', 'name' => 'User Registration'),
    'default_role' => array('expected' => 'subscriber', 'name' => 'Default User Role')
);

foreach ($settings_to_check as $option => $config) {
    $current_value = get_option($option);
    $expected = $config['expected'];
    $status = ($current_value == $expected) ? '<span class="success">OK</span>' : '<span class="error">WRONG</span>';
    
    echo "<tr>";
    echo "<td>{$config['name']}</td>";
    echo "<td>{$current_value}</td>";
    echo "<td>{$expected}</td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo '</table>';
echo '</div>';

// Test 4: Function Existence
echo '<div class="test-section">';
echo '<h2>Test 4: Required Functions</h2>';

$required_functions = array(
    'ln_reader_create_custom_pages',
    'ln_reader_handle_login',
    'ln_reader_handle_registration',
    'ln_reader_handle_password_reset',
    'ln_reader_custom_login_redirect',
    'ln_reader_hide_admin_bar'
);

echo '<table>';
echo '<tr><th>Function</th><th>Status</th></tr>';

foreach ($required_functions as $function) {
    $status = function_exists($function) ? '<span class="success">EXISTS</span>' : '<span class="error">MISSING</span>';
    echo "<tr><td>{$function}</td><td>{$status}</td></tr>";
}

echo '</table>';
echo '</div>';

// Test 5: URL Testing
echo '<div class="test-section">';
echo '<h2>Test 5: URL Accessibility Test</h2>';

echo '<p>Click the links below to test if the pages are accessible:</p>';
echo '<ul>';
foreach ($pages_to_check as $slug) {
    if ($pages_status[$slug]) {
        $url = home_url('/' . $slug);
        echo "<li><a href='{$url}' target='_blank'>{$slug} page</a> - {$url}</li>";
    } else {
        echo "<li><span class='error'>{$slug} page - NOT AVAILABLE</span></li>";
    }
}
echo '</ul>';
echo '</div>';

// Test 6: Login Redirect Test
echo '<div class="test-section">';
echo '<h2>Test 6: Login Redirect Test</h2>';

echo '<p>Test the login redirect by trying to access wp-login.php:</p>';
echo '<p><a href="' . site_url('/wp-login.php') . '" target="_blank">Try wp-login.php</a> (should redirect to custom login page)</p>';
echo '<p class="info">Note: If you\'re already logged in as admin, the redirect might not work as expected.</p>';
echo '</div>';

// Handle page creation requests
if (isset($_GET['create_page'])) {
    $page_to_create = sanitize_text_field($_GET['create_page']);
    
    if (in_array($page_to_create, $pages_to_check)) {
        echo '<div class="test-section">';
        echo '<h2>Creating Page: ' . $page_to_create . '</h2>';
        
        // Call the page creation function
        ln_reader_create_custom_pages();
        
        echo '<p class="success">Page creation function called. <a href="?">Refresh to see results</a></p>';
        echo '</div>';
    }
}

// Test 7: Quick Actions
echo '<div class="test-section">';
echo '<h2>Quick Actions</h2>';
echo '<p><a href="?action=create_all_pages">Create All Missing Pages</a></p>';
echo '<p><a href="?action=flush_rules">Flush Rewrite Rules</a></p>';
echo '<p><a href="?action=fix_settings">Fix WordPress Settings</a></p>';
echo '</div>';

// Handle quick actions
if (isset($_GET['action'])) {
    $action = sanitize_text_field($_GET['action']);
    
    echo '<div class="test-section">';
    echo '<h2>Action Result</h2>';
    
    switch ($action) {
        case 'create_all_pages':
            ln_reader_create_custom_pages();
            echo '<p class="success">All pages creation attempted. <a href="?">Refresh to see results</a></p>';
            break;
            
        case 'flush_rules':
            flush_rewrite_rules();
            echo '<p class="success">Rewrite rules flushed successfully.</p>';
            break;
            
        case 'fix_settings':
            update_option('users_can_register', 1);
            update_option('default_role', 'subscriber');
            echo '<p class="success">WordPress settings updated.</p>';
            break;
    }
    
    echo '</div>';
}

echo '<div class="test-section">';
echo '<h2>Summary</h2>';

$all_pages_exist = array_reduce($pages_status, function($carry, $item) {
    return $carry && $item;
}, true);

if ($all_pages_exist) {
    echo '<p class="success">✓ All custom pages exist and should be working.</p>';
} else {
    echo '<p class="error">✗ Some pages are missing. Use the quick actions above to fix.</p>';
}

echo '<p><strong>Next Steps:</strong></p>';
echo '<ol>';
echo '<li>Ensure all pages exist (green status above)</li>';
echo '<li>Test user registration and login</li>';
echo '<li>Check that wp-login.php redirects properly</li>';
echo '<li>Delete this test file after verification</li>';
echo '</ol>';

echo '</div>';

echo '<hr>';
echo '<p><em>Test completed at ' . date('Y-m-d H:i:s') . '</em></p>';
echo '<p><strong>Remember to delete this file after testing for security reasons.</strong></p>';
?>
