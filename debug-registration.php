<?php
/**
 * Debug Registration Process
 * This script will test the registration functionality step by step
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Registration Process Debug</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
    .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f8f9fa; }
</style>';

// Handle test registration
if (isset($_POST['test_register'])) {
    echo '<div class="step">';
    echo '<h2>Testing Registration Process</h2>';
    
    $test_username = 'testuser_' . time();
    $test_email = 'test_' . time() . '@example.com';
    $test_password = 'testpass123';
    
    echo "<p><strong>Test Data:</strong></p>";
    echo "<ul>";
    echo "<li>Username: {$test_username}</li>";
    echo "<li>Email: {$test_email}</li>";
    echo "<li>Password: {$test_password}</li>";
    echo "</ul>";
    
    // Test wp_create_user directly
    echo "<h3>Step 1: Testing wp_create_user()</h3>";
    $user_id = wp_create_user($test_username, $test_password, $test_email);
    
    if (is_wp_error($user_id)) {
        echo "<p class='error'>✗ wp_create_user() failed: " . $user_id->get_error_message() . "</p>";
    } else {
        echo "<p class='success'>✓ wp_create_user() succeeded. User ID: {$user_id}</p>";
        
        // Check if user exists in database
        $user = get_user_by('id', $user_id);
        if ($user) {
            echo "<p class='success'>✓ User found in database</p>";
            echo "<p>User details:</p>";
            echo "<ul>";
            echo "<li>ID: {$user->ID}</li>";
            echo "<li>Login: {$user->user_login}</li>";
            echo "<li>Email: {$user->user_email}</li>";
            echo "<li>Role: " . implode(', ', $user->roles) . "</li>";
            echo "<li>Status: {$user->user_status}</li>";
            echo "</ul>";
            
            // Check if user appears in admin
            $admin_url = admin_url('users.php');
            echo "<p><a href='{$admin_url}' target='_blank'>Check WordPress Admin Users List</a></p>";
        } else {
            echo "<p class='error'>✗ User not found in database after creation</p>";
        }
    }
    echo '</div>';
}

echo '<div class="step">';
echo '<h2>Step 1: WordPress Settings Check</h2>';

$settings = array(
    'users_can_register' => get_option('users_can_register'),
    'default_role' => get_option('default_role'),
    'blog_public' => get_option('blog_public'),
    'users_can_register_option' => get_option('users_can_register')
);

echo '<table>';
echo '<tr><th>Setting</th><th>Value</th><th>Status</th></tr>';
foreach ($settings as $key => $value) {
    $status = 'info';
    $display_value = $value;
    
    if ($key === 'users_can_register') {
        $status = $value ? 'success' : 'error';
        $display_value = $value ? 'Enabled' : 'Disabled';
    } elseif ($key === 'default_role') {
        $status = ($value === 'subscriber') ? 'success' : 'warning';
    }
    
    echo "<tr><td>{$key}</td><td>{$display_value}</td><td class='{$status}'>" . 
         ($status === 'success' ? '✓' : ($status === 'error' ? '✗' : '⚠')) . "</td></tr>";
}
echo '</table>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 2: Registration Function Check</h2>';

$function_exists = function_exists('ln_reader_handle_registration');
echo "<p><strong>ln_reader_handle_registration() exists:</strong> " . 
     ($function_exists ? '<span class="success">✓ Yes</span>' : '<span class="error">✗ No</span>') . "</p>";

if ($function_exists) {
    // Check if function is hooked
    $hooked = has_action('init', 'ln_reader_handle_registration');
    echo "<p><strong>Function hooked to 'init':</strong> " . 
         ($hooked ? '<span class="success">✓ Yes</span>' : '<span class="error">✗ No</span>') . "</p>";
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 3: Register Page Check</h2>';

$register_page = get_page_by_path('register');
if ($register_page) {
    echo "<p class='success'>✓ Register page exists (ID: {$register_page->ID})</p>";
    echo "<p><strong>Status:</strong> {$register_page->post_status}</p>";
    echo "<p><strong>Template:</strong> " . get_post_meta($register_page->ID, '_wp_page_template', true) . "</p>";
    echo "<p><strong>URL:</strong> <a href='" . get_permalink($register_page->ID) . "' target='_blank'>" . 
         get_permalink($register_page->ID) . "</a></p>";
} else {
    echo "<p class='error'>✗ Register page does not exist</p>";
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 4: Database Permissions Check</h2>';

// Test database write permissions
global $wpdb;

// Check if we can write to users table
$test_query = $wpdb->prepare("SELECT COUNT(*) FROM {$wpdb->users} WHERE user_login = %s", 'nonexistent_user_test');
$result = $wpdb->get_var($test_query);

if ($result !== null) {
    echo "<p class='success'>✓ Can read from wp_users table</p>";
} else {
    echo "<p class='error'>✗ Cannot read from wp_users table</p>";
    echo "<p class='error'>Database error: " . $wpdb->last_error . "</p>";
}

// Check current user count
$user_count = count_users();
echo "<p><strong>Current user count:</strong> {$user_count['total_users']}</p>";
echo "</div>";

echo '<div class="step">';
echo '<h2>Step 5: Error Log Check</h2>';

$error_log_path = WP_CONTENT_DIR . '/debug.log';
if (file_exists($error_log_path)) {
    $log_content = file_get_contents($error_log_path);
    $recent_lines = array_slice(explode("\n", $log_content), -10);
    
    echo "<p><strong>Recent error log entries:</strong></p>";
    echo "<div class='code'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            echo esc_html($line) . "<br>";
        }
    }
    echo "</div>";
} else {
    echo "<p class='warning'>⚠ No debug.log file found. Enable WP_DEBUG_LOG to see errors.</p>";
}
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 6: Test Registration</h2>';
echo '<p>Click the button below to test the registration process with a dummy user:</p>';
echo '<form method="post">';
echo '<button type="submit" name="test_register" class="button" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">Test Registration</button>';
echo '</form>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Step 7: Manual Registration Test</h2>';
echo '<p>You can also test registration manually:</p>';
echo '<ol>';
echo '<li>Go to the <a href="' . home_url('/register') . '" target="_blank">Registration Page</a></li>';
echo '<li>Fill out the form with test data</li>';
echo '<li>Submit the form</li>';
echo '<li>Check the <a href="' . admin_url('users.php') . '" target="_blank">WordPress Users List</a></li>';
echo '</ol>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Debugging Tips</h2>';
echo '<ul>';
echo '<li>Enable WordPress debug mode by adding these lines to wp-config.php:</li>';
echo '<div class="code">define(\'WP_DEBUG\', true);<br>define(\'WP_DEBUG_LOG\', true);</div>';
echo '<li>Check the error log at: ' . WP_CONTENT_DIR . '/debug.log</li>';
echo '<li>Verify that the registration form is submitting to the correct URL</li>';
echo '<li>Check browser developer tools for JavaScript errors</li>';
echo '</ul>';
echo '</div>';

echo '<hr>';
echo '<p><em>Debug completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
