<?php
/**
 * Simple Authentication Pages Creator
 * Run this script to create the authentication pages
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php. Please run this script from the theme directory.');
}

echo '<h1>Creating Authentication Pages</h1>';
echo '<style>body { font-family: Arial, sans-serif; margin: 40px; } .success { color: green; } .error { color: red; }</style>';

// Enable user registration
update_option('users_can_register', 1);
update_option('default_role', 'subscriber');
echo '<p class="success">✓ User registration enabled</p>';

// Pages to create
$pages = array(
    'login' => array(
        'title' => 'Login',
        'template' => 'page-login.php'
    ),
    'register' => array(
        'title' => 'Register',
        'template' => 'page-register.php'
    ),
    'dashboard' => array(
        'title' => 'Dashboard',
        'template' => 'page-dashboard.php'
    ),
    'reset-password' => array(
        'title' => 'Reset Password',
        'template' => 'page-reset-password.php'
    )
);

foreach ($pages as $slug => $page_data) {
    echo "<h3>Creating: {$page_data['title']} ({$slug})</h3>";
    
    // Check if page already exists
    $existing_page = get_page_by_path($slug);
    
    if ($existing_page) {
        echo "<p>Page already exists with ID: {$existing_page->ID}</p>";
        
        // Update template if needed
        $current_template = get_post_meta($existing_page->ID, '_wp_page_template', true);
        if ($current_template !== $page_data['template']) {
            update_post_meta($existing_page->ID, '_wp_page_template', $page_data['template']);
            echo "<p class='success'>✓ Updated template to: {$page_data['template']}</p>";
        } else {
            echo "<p>Template is already correct: {$page_data['template']}</p>";
        }
        
        // Ensure page is published
        if ($existing_page->post_status !== 'publish') {
            wp_update_post(array(
                'ID' => $existing_page->ID,
                'post_status' => 'publish'
            ));
            echo "<p class='success'>✓ Page published</p>";
        }
        
        echo "<p>URL: <a href='" . get_permalink($existing_page->ID) . "' target='_blank'>" . get_permalink($existing_page->ID) . "</a></p>";
    } else {
        // Create new page
        $page_data_wp = array(
            'post_title' => $page_data['title'],
            'post_name' => $slug,
            'post_content' => '',
            'post_status' => 'publish',
            'post_type' => 'page',
            'post_author' => 1
        );
        
        $page_id = wp_insert_post($page_data_wp);
        
        if ($page_id && !is_wp_error($page_id)) {
            // Set template
            update_post_meta($page_id, '_wp_page_template', $page_data['template']);
            
            echo "<p class='success'>✓ Successfully created page with ID: {$page_id}</p>";
            echo "<p class='success'>✓ Template set to: {$page_data['template']}</p>";
            echo "<p>URL: <a href='" . get_permalink($page_id) . "' target='_blank'>" . get_permalink($page_id) . "</a></p>";
        } else {
            echo "<p class='error'>✗ Failed to create page</p>";
            if (is_wp_error($page_id)) {
                echo "<p class='error'>Error: " . $page_id->get_error_message() . "</p>";
            }
        }
    }
    
    echo '<hr>';
}

// Flush rewrite rules
flush_rewrite_rules();
echo "<p class='success'>✓ Flushed rewrite rules</p>";

// Check template files
echo '<h2>Checking Template Files</h2>';
$theme_dir = get_template_directory();
$required_templates = array(
    'page-login.php',
    'page-register.php',
    'page-dashboard.php',
    'page-reset-password.php'
);

foreach ($required_templates as $template) {
    $template_path = $theme_dir . '/' . $template;
    if (file_exists($template_path)) {
        echo "<p class='success'>✓ {$template} exists</p>";
    } else {
        echo "<p class='error'>✗ {$template} is missing!</p>";
    }
}

echo '<h2>Test Links</h2>';
echo '<ul>';
echo '<li><a href="' . home_url('/login') . '" target="_blank">Login Page</a></li>';
echo '<li><a href="' . home_url('/register') . '" target="_blank">Register Page</a></li>';
echo '<li><a href="' . home_url('/dashboard') . '" target="_blank">Dashboard Page</a></li>';
echo '<li><a href="' . home_url('/reset-password') . '" target="_blank">Reset Password Page</a></li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Script completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
