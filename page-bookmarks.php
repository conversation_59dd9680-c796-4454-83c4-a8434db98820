<?php
/*
Template Name: Bookmarks
*/

get_header();
?>

<div class="container py-4">
    <h1 class="mb-4">My Bookmarks</h1>
    
    <?php if (!is_user_logged_in()) : ?>
        <div class="alert alert-info">
            Please log in to view your bookmarks.
        </div>
    <?php else :
        $user_id = get_current_user_id();
        $bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
        
        if (empty($bookmarked_novels)) : ?>
            <div class="alert alert-info">
                You haven't bookmarked any novels yet.
            </div>
        <?php else :
            $novel_query = new WP_Query(array(
                'post_type' => 'novel',
                'post__in' => $bookmarked_novels,
                'posts_per_page' => -1
            ));

            if ($novel_query->have_posts()) : 
                while ($novel_query->have_posts()) : $novel_query->the_post(); 
                    // Get latest chapters
                    $chapters = get_posts(array(
                        'post_type' => 'post',
                        'posts_per_page' => 2,
                        'orderby' => 'date',
                        'order' => 'DESC',
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'category',
                                'field' => 'name',
                                'terms' => get_the_title()
                            )
                        )
                    ));
                    ?>
                    <div class="novel-item mb-4 border-bottom pb-4">
                        <div class="row">
                            <div class="col-auto">
                                <div class="position-relative">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <?php the_post_thumbnail('thumbnail', array('class' => 'novel-cover', 'style' => 'width: 120px; height: 180px; object-fit: cover;')); ?>
                                    <?php else : ?>
                                        <img src="<?php echo get_template_directory_uri(); ?>/images/default-cover.jpg" class="novel-cover" style="width: 120px; height: 180px; object-fit: cover;" alt="Default Cover">
                                    <?php endif; ?>
                                    <?php 
                                    $chapter_count = count(get_posts(array(
                                        'post_type' => 'post',
                                        'posts_per_page' => -1,
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => 'category',
                                                'field' => 'name',
                                                'terms' => get_the_title()
                                            )
                                        )
                                    )));
                                    ?>
                                    <span class="position-absolute top-0 start-0 bg-danger text-white px-2 rounded-end"><?php echo $chapter_count; ?></span>
                                </div>
                            </div>
                            <div class="col">
                                <h3 class="h5 mb-3">
                                    <a href="<?php the_permalink(); ?>" class="text-decoration-none text-dark">
                                        <?php the_title(); ?>
                                    </a>
                                </h3>
                                <?php 
                                $reading_progress = get_user_reading_progress(get_the_ID());
                                $latest_chapter = get_latest_chapter(get_the_ID());
                                $last_chapter_id = isset($reading_progress) ? $reading_progress : false;
                                $last_chapter = $last_chapter_id ? get_post($last_chapter_id) : false;
                                if ($last_chapter) : 
                                    $chapter_number = intval(get_post_meta($last_chapter->ID, '_chapter_number', true));
                                    $novel_slug = get_post_field('post_name', get_the_ID());
                                    $continue_url = home_url("/{$novel_slug}/chapter-{$chapter_number}");
                                ?>
                                    <a href="<?php echo esc_url($continue_url); ?>" class="btn btn-sm btn-primary mb-3">
                                        Continue Reading
                                    </a>
                                <?php endif; ?>
                                <?php foreach ($chapters as $chapter) : ?>
                                    <div class="chapter-item mb-2">
                                        <a href="<?php echo get_permalink($chapter->ID); ?>" class="text-decoration-none">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="chapter-title text-muted">
                                                    <?php echo $chapter->post_title; ?>
                                                </span>
                                                <span class="chapter-time text-muted">
                                                    <?php echo human_time_diff(get_post_time('U', false, $chapter), current_time('timestamp')) . ' ago'; ?>
                                                </span>
                                            </div>
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                                <?php 
                                if ($reading_progress && $latest_chapter) : 
                                    if ($last_chapter) :
                                ?>
                                    <div class="reading-progress mt-2 text-muted">
                                        <small>
                                            Last Read: <?php echo $last_chapter->post_title; ?>
                                            <?php if ($last_chapter->ID !== $latest_chapter->ID) : ?>
                                                <span class="text-danger">
                                                    (<?php echo count(get_posts(array(
                                                        'post_type' => 'post',
                                                        'posts_per_page' => -1,
                                                        'orderby' => 'date',
                                                        'order' => 'ASC',
                                                        'tax_query' => array(
                                                            array(
                                                                'taxonomy' => 'category',
                                                                'field' => 'name',
                                                                'terms' => get_the_title()
                                                            )
                                                        ),
                                                        'date_query' => array(
                                                            'after' => get_the_date('Y-m-d H:i:s', $last_chapter)
                                                        )
                                                    ))); ?> new)
                                                </span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                <?php 
                                    endif;
                                endif;
                                ?>
                            </div>
                            <div class="col-auto d-flex align-items-start">
                                <div class="d-flex flex-column align-items-end">
                                    <span class="text-muted">Updated Time</span>
                                    <span class="text-muted"><?php echo get_the_modified_date('F j, Y'); ?></span>
                                    <button class="btn btn-link text-decoration-none p-0 mt-2 remove-bookmark" data-novel-id="<?php echo get_the_ID(); ?>">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endwhile;
                wp_reset_postdata();
            endif;
        endif;
    endif; ?>
</div>

<style>
.novel-cover {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.chapter-item:hover {
    background-color: rgba(0,0,0,0.03);
    border-radius: 4px;
}
.chapter-item a {
    display: block;
    padding: 8px;
    color: inherit;
}
.remove-bookmark {
    color: #6c757d;
}
.remove-bookmark:hover {
    color: #dc3545;
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.remove-bookmark').on('click', function() {
        var novelId = $(this).data('novel-id');
        var $novelItem = $(this).closest('.novel-item');
        var $button = $(this);

        if (confirm('Are you sure you want to remove this bookmark?')) {
            // Show loading state
            $button.html('<i class="fas fa-spinner fa-spin"></i>').prop('disabled', true);

            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: {
                    action: 'toggle_bookmark',
                    novel_id: novelId,
                    security: '<?php echo wp_create_nonce('bookmark_nonce'); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        // Successful removal
                        $novelItem.fadeOut(300, function() {
                            $(this).remove();
                            if ($('.novel-item').length === 0) {
                                location.reload();
                            }
                        });
                    } else {
                        // Server error
                        alert('Failed to remove bookmark: ' + (response.data || 'Unknown server error'));
                        // Reset button
                        $button.html('<i class="fas fa-times"></i>').prop('disabled', false);
                    }
                },
                error: function(xhr, status, error) {
                    // Network or other errors
                    console.error('AJAX error:', error);
                    alert('Failed to remove bookmark. Please check your connection and try again.');
                    // Reset button
                    $button.html('<i class="fas fa-times"></i>').prop('disabled', false);
                }
            });
        }
    });
});
</script>

<?php get_footer(); ?>
