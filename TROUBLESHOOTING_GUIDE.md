# LN Reader Authentication System - Troubleshooting Guide

## Quick Fix Steps

### Step 1: Run the Page Creation Script
1. Upload `create-auth-pages.php` to your theme directory
2. Visit: `http://localhost/epic/wp-content/themes/lnreader/create-auth-pages.php`
3. This will create all required pages and configure settings
4. Delete the file after use

### Step 2: Test the System
1. Upload `test-auth-system.php` to your theme directory  
2. Visit: `http://localhost/epic/wp-content/themes/lnreader/test-auth-system.php`
3. Check all tests pass (green status)
4. Use quick actions to fix any issues
5. Delete the file after use

### Step 3: Manual Verification
1. Visit `http://localhost/epic/login` - should show custom login page
2. Visit `http://localhost/epic/register` - should show custom registration page
3. Visit `http://localhost/epic/wp-login.php` - should redirect to custom login page

## Common Issues and Solutions

### Issue 1: 404 Error on Custom Pages

**Symptoms:**
- `/login`, `/register`, `/dashboard` return 404 errors
- Pages don't exist in WordPress admin

**Solutions:**
1. **Run page creation script** (see Step 1 above)
2. **Manual page creation in WordPress admin:**
   - Go to Pages > Add New
   - Create pages with exact slugs: `login`, `register`, `dashboard`, `reset-password`
   - Set page templates in Page Attributes
3. **Flush permalinks:**
   - Go to Settings > Permalinks
   - Click "Save Changes" (no changes needed)

### Issue 2: wp-login.php Still Accessible

**Symptoms:**
- `wp-login.php` doesn't redirect to custom login page
- Console error: `GET http://localhost/epic/wp-login.php 503`

**Solutions:**
1. **Check function priority:**
   ```php
   // Add this to functions.php if redirect isn't working
   function force_login_redirect() {
       if (strpos($_SERVER['REQUEST_URI'], 'wp-login.php') !== false && !isset($_GET['action'])) {
           wp_redirect(home_url('/login'));
           exit;
       }
   }
   add_action('init', 'force_login_redirect', 1);
   ```

2. **Clear cache** if using caching plugins
3. **Check .htaccess** for conflicting rules

### Issue 3: Registration Not Working

**Symptoms:**
- Registration form doesn't submit
- "Registration is disabled" message

**Solutions:**
1. **Enable registration in WordPress:**
   - Go to Settings > General
   - Check "Anyone can register"
   - Set default role to "Subscriber"

2. **Or add to functions.php:**
   ```php
   update_option('users_can_register', 1);
   update_option('default_role', 'subscriber');
   ```

### Issue 4: Dashboard Access Issues

**Symptoms:**
- Users can't access dashboard
- Redirected to WordPress admin

**Solutions:**
1. **Check user capabilities:**
   ```php
   // Add this debug code temporarily
   function debug_user_caps() {
       if (is_user_logged_in()) {
           $user = wp_get_current_user();
           error_log('User roles: ' . print_r($user->roles, true));
           error_log('Can admin: ' . (current_user_can('administrator') ? 'yes' : 'no'));
       }
   }
   add_action('init', 'debug_user_caps');
   ```

2. **Verify redirect functions are working**

### Issue 5: Template Not Loading

**Symptoms:**
- Custom pages show default page template
- Authentication forms not displaying

**Solutions:**
1. **Check template files exist:**
   - `page-login.php`
   - `page-register.php`
   - `page-dashboard.php`
   - `page-reset-password.php`

2. **Verify page templates are set:**
   - Edit each page in WordPress admin
   - Check "Page Attributes" > "Template"
   - Should show custom template name

3. **Clear any caching**

## Debug Mode

### Enable WordPress Debug
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Check Error Logs
- Look in `/wp-content/debug.log`
- Check server error logs
- Use browser developer tools console

### Debug URLs
Add `?debug_pages=1` to admin URL to see page status:
`http://localhost/epic/wp-admin/?debug_pages=1`

## Manual Database Fixes

### If Pages Are Missing from Database
```sql
-- Check if pages exist
SELECT * FROM wp_posts WHERE post_name IN ('login', 'register', 'dashboard', 'reset-password') AND post_type = 'page';

-- Check page templates
SELECT p.post_title, p.post_name, pm.meta_value as template 
FROM wp_posts p 
LEFT JOIN wp_postmeta pm ON p.ID = pm.post_id AND pm.meta_key = '_wp_page_template'
WHERE p.post_name IN ('login', 'register', 'dashboard', 'reset-password') AND p.post_type = 'page';
```

### Reset Rewrite Rules
```php
// Add to functions.php temporarily
function force_flush_rules() {
    flush_rewrite_rules();
    delete_option('rewrite_rules');
}
add_action('init', 'force_flush_rules');
```

## Testing Checklist

### Basic Functionality
- [ ] Custom pages accessible (login, register, dashboard, reset-password)
- [ ] wp-login.php redirects to custom login
- [ ] User registration works
- [ ] User login works
- [ ] Dashboard displays after login
- [ ] Non-admin users can't access WordPress admin
- [ ] Admin bar hidden for regular users

### Advanced Features
- [ ] Password reset via email works
- [ ] Profile update works
- [ ] Password change works
- [ ] Bookmark management works
- [ ] Reading history displays
- [ ] Tab navigation works in dashboard

## Performance Considerations

### Optimize for Production
1. **Remove debug scripts** after testing
2. **Minify CSS/JS** files
3. **Enable caching** for static assets
4. **Optimize database** queries in dashboard

### Security Checklist
- [ ] All forms use nonces
- [ ] Input sanitization implemented
- [ ] User capability checks in place
- [ ] Debug scripts removed
- [ ] Error messages don't reveal sensitive info

## Getting Help

### Information to Provide
1. WordPress version
2. PHP version
3. Active plugins list
4. Theme version
5. Error messages from logs
6. Steps to reproduce issue

### Useful Debug Commands
```php
// Check if functions exist
var_dump(function_exists('ln_reader_create_custom_pages'));

// Check page existence
var_dump(get_page_by_path('login'));

// Check user capabilities
var_dump(current_user_can('administrator'));

// Check WordPress settings
var_dump(get_option('users_can_register'));
```

## Recovery Steps

### If System Breaks Completely
1. **Backup current files**
2. **Deactivate custom authentication:**
   - Comment out authentication functions in functions.php
   - Remove custom rewrite rules
3. **Restore WordPress defaults:**
   - Delete custom pages
   - Clear rewrite rules
4. **Re-implement step by step**

### Emergency Access
If locked out of admin:
1. **Direct database access** to create admin user
2. **FTP access** to modify functions.php
3. **wp-cli** commands to reset user passwords

Remember: Always backup your site before making changes!
