# RSS Feed Documentation

## Overview
Your WordPress site now has a properly configured RSS feed system that displays novel chapter updates in the same format as the reference site (https://www.ossantl.my.id/feed/).

## Available Feeds

### 1. Main RSS Feed
- **URL**: `http://localhost/epic/feed/`
- **Purpose**: Primary RSS feed that shows latest novel chapters
- **Format**: Matches the reference site format
- **Content**: Shows only posts that are chapters (have `_novel_id` meta field)

### 2. Custom Latest Chapters Feed
- **URL**: `http://localhost/epic/feed/latest-chapters/`
- **Purpose**: Alternative feed with custom formatting
- **Format**: Clean XML format specifically for novel chapters

## Feed Content Format

Each feed item includes:
- **Title**: `[Novel Title] Chapter [Number]` (e.g., "Starting from Zero: Building a Gaming Empire Chapter 1")
- **Link**: Direct link to the chapter page
- **Description**: Formatted text with:
  - Novel: [Novel Title]
  - Chapter: [Chapter Number]
  - Posted: [Date]
  - Link: [Chapter URL]
- **Category**: Novel title
- **Publication Date**: Chapter publication date

## How It Works

### 1. Feed Query Modification
The `modify_main_feed_query()` function ensures that:
- Only posts with `_novel_id` meta field are included (chapters only)
- Maximum 20 items are shown
- Items are ordered by publication date (newest first)

### 2. Title Customization
The `customize_feed_title()` function:
- Retrieves the novel title from the `_novel_id` meta field
- Gets the chapter number from `_chapter_number` meta field
- Formats the title as "Novel Title Chapter X"

### 3. Content Customization
The `customize_feed_content()` function:
- Replaces default post content with structured information
- Shows novel name, chapter number, date, and link
- Applies to both description and content fields

## Technical Implementation

### Functions Added to functions.php:
1. `modify_main_feed_query()` - Filters feed query
2. `customize_feed_title()` - Customizes feed item titles
3. `customize_feed_content()` - Customizes feed item content
4. `generate_latest_chapters_feed()` - Custom feed generator
5. `add_latest_chapters_feed()` - Registers custom feed
6. `set_default_site_description()` - Sets default site description

### WordPress Hooks Used:
- `pre_get_posts` - Modifies feed query
- `the_title_rss` - Customizes feed titles
- `the_excerpt_rss` - Customizes feed descriptions
- `the_content_feed` - Customizes feed content
- `init` - Registers custom feeds

## Testing the Feed

### Manual Testing:
```bash
# Test main feed
curl -s "http://localhost/epic/feed/" | head -50

# Test custom feed
curl -s "http://localhost/epic/feed/latest-chapters/" | head -50
```

### Browser Testing:
- Visit `http://localhost/epic/feed/` in your browser
- The feed should display properly formatted XML
- Each item should show novel title + chapter number

## Troubleshooting

### If Feed Doesn't Update:
1. Flush rewrite rules: Visit `http://localhost/epic/?flush_feed=1`
2. Check if chapters have proper meta fields (`_novel_id` and `_chapter_number`)
3. Verify that posts are published (not drafts)

### If Feed Shows Wrong Content:
1. Ensure chapters have `_novel_id` meta field set
2. Check that the novel post exists and is published
3. Verify `_chapter_number` meta field is set correctly

### If Feed Doesn't Load:
1. Check WordPress permalink settings
2. Ensure theme is properly activated
3. Verify no plugin conflicts

## Comparison with Reference Site

Your feed now matches the reference site format:
- ✅ Shows novel chapters only
- ✅ Proper title format: "Novel Title Chapter X"
- ✅ Structured description with novel info
- ✅ Correct publication dates
- ✅ Valid RSS 2.0 format
- ✅ Proper XML encoding

## Future Enhancements

Possible improvements:
1. Add novel cover images to feed items
2. Include chapter excerpts in descriptions
3. Add author information
4. Create genre-specific feeds
5. Add feed caching for better performance
