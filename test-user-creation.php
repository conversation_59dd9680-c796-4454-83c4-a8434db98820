<?php
/**
 * Test User Creation
 * Simple script to test if wp_create_user() works correctly
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

// Must be admin to run this
if (!current_user_can('administrator')) {
    die('Access denied. Administrator privileges required.');
}

echo '<h1>User Creation Test</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .step { background: #f8f9fa; padding: 15px; margin: 10px 0; border-left: 4px solid #007bff; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f8f9fa; }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
</style>';

// Handle user creation test
if (isset($_POST['create_test_user'])) {
    echo '<div class="step">';
    echo '<h2>Creating Test User</h2>';
    
    $timestamp = time();
    $test_username = 'testuser_' . $timestamp;
    $test_email = 'test_' . $timestamp . '@example.com';
    $test_password = 'TestPass123!';
    
    echo "<p><strong>Test User Data:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> {$test_username}</li>";
    echo "<li><strong>Email:</strong> {$test_email}</li>";
    echo "<li><strong>Password:</strong> {$test_password}</li>";
    echo "</ul>";
    
    // Enable registration if not enabled
    if (!get_option('users_can_register')) {
        update_option('users_can_register', 1);
        echo "<p class='info'>✓ Enabled user registration</p>";
    }
    
    // Set default role
    if (get_option('default_role') !== 'subscriber') {
        update_option('default_role', 'subscriber');
        echo "<p class='info'>✓ Set default role to subscriber</p>";
    }
    
    // Test user creation
    echo "<h3>Step 1: Testing wp_create_user()</h3>";
    $user_id = wp_create_user($test_username, $test_password, $test_email);
    
    if (is_wp_error($user_id)) {
        echo "<p class='error'>✗ Failed to create user</p>";
        echo "<p class='error'>Error: " . $user_id->get_error_message() . "</p>";
        
        // Additional debugging
        echo "<h4>Debug Information:</h4>";
        echo "<ul>";
        echo "<li>Username exists: " . (username_exists($test_username) ? 'Yes' : 'No') . "</li>";
        echo "<li>Email exists: " . (email_exists($test_email) ? 'Yes' : 'No') . "</li>";
        echo "<li>Is valid email: " . (is_email($test_email) ? 'Yes' : 'No') . "</li>";
        echo "<li>Users can register: " . (get_option('users_can_register') ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
    } else {
        echo "<p class='success'>✓ User created successfully!</p>";
        echo "<p><strong>User ID:</strong> {$user_id}</p>";
        
        // Get user details
        $user = get_user_by('id', $user_id);
        if ($user) {
            echo "<h4>User Details:</h4>";
            echo "<table>";
            echo "<tr><th>Property</th><th>Value</th></tr>";
            echo "<tr><td>ID</td><td>{$user->ID}</td></tr>";
            echo "<tr><td>Login</td><td>{$user->user_login}</td></tr>";
            echo "<tr><td>Email</td><td>{$user->user_email}</td></tr>";
            echo "<tr><td>Display Name</td><td>{$user->display_name}</td></tr>";
            echo "<tr><td>Roles</td><td>" . implode(', ', $user->roles) . "</td></tr>";
            echo "<tr><td>Status</td><td>{$user->user_status}</td></tr>";
            echo "<tr><td>Registration Date</td><td>{$user->user_registered}</td></tr>";
            echo "</table>";
            
            // Check if user appears in admin
            echo "<p><a href='" . admin_url('users.php') . "' target='_blank' class='btn'>View Users in Admin</a></p>";
            
            // Test login
            echo "<h4>Testing User Login:</h4>";
            $login_test = wp_authenticate($test_username, $test_password);
            if (is_wp_error($login_test)) {
                echo "<p class='error'>✗ Login test failed: " . $login_test->get_error_message() . "</p>";
            } else {
                echo "<p class='success'>✓ Login test successful</p>";
            }
        } else {
            echo "<p class='error'>✗ User created but cannot retrieve user data</p>";
        }
    }
    echo '</div>';
}

// Handle cleanup
if (isset($_POST['cleanup_test_users'])) {
    echo '<div class="step">';
    echo '<h2>Cleaning Up Test Users</h2>';
    
    global $wpdb;
    $test_users = $wpdb->get_results("SELECT ID, user_login FROM {$wpdb->users} WHERE user_login LIKE 'testuser_%'");
    
    if (empty($test_users)) {
        echo "<p class='info'>No test users found to clean up.</p>";
    } else {
        foreach ($test_users as $user) {
            $deleted = wp_delete_user($user->ID);
            if ($deleted) {
                echo "<p class='success'>✓ Deleted user: {$user->user_login} (ID: {$user->ID})</p>";
            } else {
                echo "<p class='error'>✗ Failed to delete user: {$user->user_login}</p>";
            }
        }
    }
    echo '</div>';
}

echo '<div class="step">';
echo '<h2>Current WordPress Settings</h2>';
echo '<table>';
echo '<tr><th>Setting</th><th>Value</th></tr>';
echo '<tr><td>Users Can Register</td><td>' . (get_option('users_can_register') ? 'Yes' : 'No') . '</td></tr>';
echo '<tr><td>Default Role</td><td>' . get_option('default_role') . '</td></tr>';
echo '<tr><td>WordPress Version</td><td>' . get_bloginfo('version') . '</td></tr>';
echo '<tr><td>PHP Version</td><td>' . PHP_VERSION . '</td></tr>';
echo '<tr><td>MySQL Version</td><td>' . $wpdb->db_version() . '</td></tr>';
echo '</table>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Current Users Count</h2>';
$user_count = count_users();
echo '<table>';
echo '<tr><th>Role</th><th>Count</th></tr>';
foreach ($user_count['avail_roles'] as $role => $count) {
    echo "<tr><td>{$role}</td><td>{$count}</td></tr>";
}
echo "<tr><td><strong>Total</strong></td><td><strong>{$user_count['total_users']}</strong></td></tr>";
echo '</table>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Test Actions</h2>';
echo '<form method="post" style="display: inline;">';
echo '<button type="submit" name="create_test_user" class="btn">Create Test User</button>';
echo '</form>';

echo '<form method="post" style="display: inline;">';
echo '<button type="submit" name="cleanup_test_users" class="btn" style="background: #dc3545;">Clean Up Test Users</button>';
echo '</form>';
echo '</div>';

echo '<div class="step">';
echo '<h2>Registration Function Status</h2>';
$functions = array(
    'ln_reader_handle_registration',
    'wp_create_user',
    'username_exists',
    'email_exists',
    'is_email'
);

echo '<table>';
echo '<tr><th>Function</th><th>Exists</th></tr>';
foreach ($functions as $func) {
    $exists = function_exists($func);
    echo "<tr><td>{$func}</td><td>" . ($exists ? '<span class="success">✓</span>' : '<span class="error">✗</span>') . "</td></tr>";
}
echo '</table>';
echo '</div>';

echo '<hr>';
echo '<p><em>Test completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
