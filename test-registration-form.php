<?php
/**
 * Test Registration Form
 * This page simulates the registration process to test functionality
 */

// Include WordPress
$wp_load_paths = array(
    '../../../wp-load.php',
    '../../wp-load.php',
    '../wp-load.php',
    'wp-load.php'
);

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not find wp-load.php');
}

echo '<h1>Registration Form Test</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    .form-group { margin-bottom: 15px; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="text"], input[type="email"], input[type="password"] { 
        width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
    }
    .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    .alert { padding: 15px; margin: 15px 0; border-radius: 4px; }
    .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
</style>';

// Handle form submission
if (isset($_POST['test_registration'])) {
    echo '<div class="alert alert-info">';
    echo '<h3>Processing Registration...</h3>';
    
    $username = sanitize_user($_POST['username']);
    $email = sanitize_email($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    echo "<p><strong>Submitted Data:</strong></p>";
    echo "<ul>";
    echo "<li>Username: " . esc_html($username) . "</li>";
    echo "<li>Email: " . esc_html($email) . "</li>";
    echo "<li>Password: " . str_repeat('*', strlen($password)) . "</li>";
    echo "</ul>";
    
    // Validation
    $errors = array();
    
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        $errors[] = 'Please fill in all fields.';
    }
    
    if (!is_email($email)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (username_exists($username)) {
        $errors[] = 'Username already exists.';
    }
    
    if (email_exists($email)) {
        $errors[] = 'Email already registered.';
    }
    
    if (strlen($password) < 6) {
        $errors[] = 'Password must be at least 6 characters long.';
    }
    
    if ($password !== $confirm_password) {
        $errors[] = 'Passwords do not match.';
    }
    
    if (!get_option('users_can_register')) {
        $errors[] = 'User registration is disabled.';
    }
    
    if (!empty($errors)) {
        echo '<div class="alert alert-danger">';
        echo '<h4>Validation Errors:</h4>';
        echo '<ul>';
        foreach ($errors as $error) {
            echo '<li>' . esc_html($error) . '</li>';
        }
        echo '</ul>';
        echo '</div>';
    } else {
        // Attempt to create user
        echo '<h4>Creating User...</h4>';
        
        $user_id = wp_create_user($username, $password, $email);
        
        if (is_wp_error($user_id)) {
            echo '<div class="alert alert-danger">';
            echo '<h4>User Creation Failed:</h4>';
            echo '<p>' . esc_html($user_id->get_error_message()) . '</p>';
            echo '</div>';
        } else {
            echo '<div class="alert alert-success">';
            echo '<h4>User Created Successfully!</h4>';
            echo '<p><strong>User ID:</strong> ' . $user_id . '</p>';
            
            // Get user details
            $user = get_user_by('id', $user_id);
            if ($user) {
                echo '<p><strong>User Details:</strong></p>';
                echo '<ul>';
                echo '<li>Login: ' . esc_html($user->user_login) . '</li>';
                echo '<li>Email: ' . esc_html($user->user_email) . '</li>';
                echo '<li>Display Name: ' . esc_html($user->display_name) . '</li>';
                echo '<li>Roles: ' . implode(', ', $user->roles) . '</li>';
                echo '<li>Registration Date: ' . $user->user_registered . '</li>';
                echo '</ul>';
                
                echo '<p><a href="' . admin_url('users.php') . '" target="_blank">Check WordPress Admin Users List</a></p>';
                
                // Test authentication
                echo '<h5>Testing Authentication:</h5>';
                $auth_test = wp_authenticate($username, $password);
                if (is_wp_error($auth_test)) {
                    echo '<p class="error">Authentication test failed: ' . $auth_test->get_error_message() . '</p>';
                } else {
                    echo '<p class="success">Authentication test passed!</p>';
                }
            }
            echo '</div>';
        }
    }
    echo '</div>';
}

// Show current settings
echo '<div style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 4px;">';
echo '<h3>Current WordPress Settings</h3>';
echo '<ul>';
echo '<li><strong>Users Can Register:</strong> ' . (get_option('users_can_register') ? 'Yes' : 'No') . '</li>';
echo '<li><strong>Default Role:</strong> ' . get_option('default_role') . '</li>';
echo '<li><strong>Current User Count:</strong> ' . count_users()['total_users'] . '</li>';
echo '</ul>';

if (!get_option('users_can_register')) {
    echo '<p class="error">⚠ User registration is disabled. Enable it in WordPress Settings > General.</p>';
}
echo '</div>';

// Registration form
echo '<h3>Test Registration Form</h3>';
echo '<form method="post">';

echo '<div class="form-group">';
echo '<label for="username">Username:</label>';
echo '<input type="text" id="username" name="username" value="testuser_' . time() . '" required>';
echo '</div>';

echo '<div class="form-group">';
echo '<label for="email">Email:</label>';
echo '<input type="email" id="email" name="email" value="test_' . time() . '@example.com" required>';
echo '</div>';

echo '<div class="form-group">';
echo '<label for="password">Password:</label>';
echo '<input type="password" id="password" name="password" value="TestPass123" required>';
echo '</div>';

echo '<div class="form-group">';
echo '<label for="confirm_password">Confirm Password:</label>';
echo '<input type="password" id="confirm_password" name="confirm_password" value="TestPass123" required>';
echo '</div>';

echo '<button type="submit" name="test_registration" class="btn">Test Registration</button>';
echo '</form>';

echo '<hr>';
echo '<h3>Additional Tests</h3>';
echo '<ul>';
echo '<li><a href="' . home_url('/register') . '" target="_blank">Visit Actual Registration Page</a></li>';
echo '<li><a href="' . admin_url('users.php') . '" target="_blank">View WordPress Users</a></li>';
echo '<li><a href="debug-registration.php">Run Registration Debug Script</a></li>';
echo '<li><a href="test-user-creation.php">Run User Creation Test</a></li>';
echo '</ul>';

echo '<hr>';
echo '<p><em>Test page loaded at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
