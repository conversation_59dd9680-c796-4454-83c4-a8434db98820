<?php get_header(); ?>

<?php 
wp_enqueue_script('jquery');
wp_localize_script('jquery', 'bookmarkAjax', array(
    'ajaxurl' => admin_url('admin-ajax.php'),
    'security' => wp_create_nonce('bookmark_nonce')
));
?>

<div class="container py-4">
    <div class="row">
        <!-- Left Column - Cover & Info -->
        <div class="col-md-3">
            <!-- Cover Image -->
            <div class="novel-cover mb-3">
                <?php 
                if (has_post_thumbnail()) {
                    the_post_thumbnail('medium', array('class' => 'img-fluid rounded shadow-sm'));
                } else {
                    echo '<img src="' . get_template_directory_uri() . '/images/default-cover.jpg" class="img-fluid rounded shadow-sm" alt="Default Cover">';
                }
                ?>
            </div>

            <!-- Rating Section -->
            <div class="rating-section mb-4">
                <?php
                $rating = get_novel_rating(get_the_ID());
                $average = isset($rating['average']) ? floatval($rating['average']) : 0;
                $count = isset($rating['count']) ? intval($rating['count']) : 0;
                $has_rated = has_user_rated(get_the_ID());
                ?>
                <div class="rating-display">
                    <div class="stars">
                        <?php for ($i = 1; $i <= 5; $i++) : ?>
                            <i class="fa<?php echo ($i <= $average) ? 's' : 'r'; ?> fa-star"></i>
                        <?php endfor; ?>
                        <span class="rating-value">(<?php echo number_format($average, 1); ?>)</span>
                        <span class="rating-count"><?php echo $count; ?> ratings</span>
                    </div>
                    <?php if (is_user_logged_in() && !$has_rated) : ?>
                        <div class="rate-novel mt-2">
                            <div class="stars-input">
                                <?php for ($i = 1; $i <= 5; $i++) : ?>
                                    <i class="far fa-star" data-rating="<?php echo $i; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <small class="text-muted">Click to rate</small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Bookmark Button -->
            <div class="bookmark-section text-center mb-4">
                <?php if (is_user_logged_in()) : 
                    $user_id = get_current_user_id();
                    $bookmarked_novels = get_user_meta($user_id, 'bookmarked_novels', true);
                    $is_bookmarked = !empty($bookmarked_novels) && in_array(get_the_ID(), $bookmarked_novels);
                    
                    // Get last read chapter
                    $reading_progress = get_user_meta($user_id, 'reading_progress', true);
                    $last_chapter_id = isset($reading_progress[get_the_ID()]) ? $reading_progress[get_the_ID()] : false;
                    $last_chapter = $last_chapter_id ? get_post($last_chapter_id) : false;
                ?>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-outline-primary toggle-bookmark" data-novel-id="<?php echo get_the_ID(); ?>" data-nonce="<?php echo wp_create_nonce('bookmark_nonce'); ?>">
                            <i class="<?php echo $is_bookmarked ? 'fas' : 'far'; ?> fa-bookmark"></i>
                            <span class="bookmark-text"><?php echo $is_bookmarked ? 'Bookmarked' : 'Add Bookmark'; ?></span>
                        </button>
                        
                        <?php 
                        if ($last_chapter && $last_chapter->ID) : 
                            $chapter_number = intval(get_post_meta($last_chapter->ID, '_chapter_number', true));
                            $novel_slug = get_post_field('post_name', get_the_ID());
                            $continue_url = home_url("/{$novel_slug}/chapter-{$chapter_number}");
                        ?>
                            <a href="<?php echo esc_url($continue_url); ?>" class="btn btn-success">
                                <i class="fas fa-book-reader"></i> Continue Chapter <?php echo $chapter_number; ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php else : ?>
                    <button class="btn btn-outline-primary" onclick="alert('Please login to bookmark novels')">
                        <i class="far fa-bookmark"></i> Add Bookmark
                    </button>
                <?php endif; ?>
            </div>

            <!-- Novel Info Card -->
            <div class="card novel-info mb-4">
                <div class="card-body p-3">
                    <table class="table table-sm mb-0">
                        <tbody>
                            <tr>
                                <td><i class="bi bi-bookmark-check"></i> Status</td>
                                <td><?php echo esc_html(get_post_meta(get_the_ID(), '_novel_status', true) ?: 'Ongoing'); ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-calendar3"></i> Release Year</td>
                                <td><?php echo esc_html(get_post_meta(get_the_ID(), '_release_year', true)); ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-person"></i> Author</td>
                                <td><?php 
                                // Coba ambil dari kedua kemungkinan meta key
                                $author = get_post_meta(get_the_ID(), '_author', true);
                                if (empty($author)) {
                                    $author = get_post_meta(get_the_ID(), '_novel_author', true);
                                }
                                echo esc_html($author ?: 'Unknown');
                                ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-person-circle"></i> Posted by</td>
                                <td><?php 
                                $author_id = get_post_field('post_author', get_the_ID());
                                $author_name = get_the_author_meta('display_name', $author_id);
                                echo esc_html($author_name);
                                ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-clock"></i> Posted on</td>
                                <td><?php echo get_the_date('M j, Y'); ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-clock-history"></i> Updated</td>
                                <td><?php echo get_the_modified_date('M j, Y'); ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-translate"></i> Language</td>
                                <td><?php echo esc_html(get_post_meta(get_the_ID(), '_native_language', true) ?: 'Unknown'); ?></td>
                            </tr>
                            <tr>
                                <td><i class="bi bi-eye"></i> Views</td>
                                <td><?php echo number_format(intval(get_post_meta(get_the_ID(), '_novel_views', true))); ?> Views</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Genres Card -->
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <i class="bi bi-tags"></i> Genres
                </div>
                <div class="card-body">
                    <?php
                    $genres = get_the_terms(get_the_ID(), 'novel_genre');
                    if ($genres && !is_wp_error($genres)) {
                        foreach ($genres as $genre) {
                            echo '<a href="' . esc_url(get_term_link($genre)) . '" class="badge bg-primary text-decoration-none me-1 mb-1">' 
                                . esc_html($genre->name) . '</a>';
                        }
                    } else {
                        echo '<span class="text-muted">No genres specified</span>';
                    }
                    ?>
                </div>
            </div>

            <!-- Novel Meta -->
            <div class="novel-meta mb-3">
                <div class="d-flex justify-content-center gap-3">
                    <span><i class="fas fa-user"></i> <?php echo get_the_author(); ?></span>
                    <span><i class="fas fa-calendar"></i> <?php echo get_the_date('F j, Y'); ?></span>
                    <span><i class="fas fa-eye"></i> <?php echo get_post_views(get_the_ID()); ?> Views</span>
                </div>
            </div>
        </div>

        <!-- Right Column - Content -->
        <div class="col-md-9">
            <div class="card mb-4">
                <div class="card-body">
                    <!-- Title -->
                    <h1 class="novel-title h2 mb-2"><?php the_title(); ?></h1>

                    <!-- Alternative Title -->
                    <?php if ($alt_title = get_post_meta(get_the_ID(), '_alternative_title', true)) : ?>
                    <div class="alternative-title mb-3">
                        <small class="text-muted">Alternative Title: <?php echo esc_html($alt_title); ?></small>
                    </div>
                    <?php endif; ?>

                    <!-- Synopsis -->
                    <div class="novel-synopsis mb-4">
                        <?php
                        $content = get_the_content();
                        $trimmed_content = wp_trim_words($content, 60, '... <a href="#" class="read-more text-primary">read more</a>');
                        ?>
                        <div class="synopsis-short">
                            <?php echo wpautop($trimmed_content); ?>
                        </div>
                        <div class="synopsis-full" style="display: none;">
                            <?php echo wpautop($content); ?>
                        </div>
                    </div>

                    <!-- Read Info -->
                    <div class="read-info">
                        <p class="text-muted small mb-0">
                            Read <?php the_title(); ?> online for free at <?php echo get_bloginfo('name'); ?>. 
                            Updates are released as soon as they're available.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Chapters -->
            <div class="card chapters-section">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h2 class="h5 mb-0"><i class="bi bi-list-ul"></i> Chapters</h2>
                    <div class="chapter-controls">
                        <select class="form-select form-select-sm d-inline-block w-auto">
                            <option value="newest">Newest First</option>
                            <option value="oldest" selected>Oldest First</option>
                        </select>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php
                    $chapters = get_posts(array(
                        'post_type' => 'post',
                        'posts_per_page' => -1,
                        'orderby' => 'meta_value_num',
                        'meta_key' => '_chapter_number',
                        'order' => 'ASC',
                        'meta_query' => array(
                            array(
                                'key' => '_novel_id',
                                'value' => get_the_ID(),
                                'compare' => '='
                            )
                        )
                    ));

                    if ($chapters) : ?>
                        <div class="chapter-list">
                            <?php foreach ($chapters as $chapter) :
                                $chapter_number = get_post_meta($chapter->ID, '_chapter_number', true);
                                $volume_number = get_post_meta($chapter->ID, '_volume_number', true);
                                
                                $display_title = '';
                                if ($volume_number) {
                                    $display_title .= 'Volume ' . esc_html($volume_number) . ' ';
                                }
                                $display_title .= 'Chapter ' . esc_html($chapter_number);
                            ?>
                                <div class="chapter-item">
                                    <a href="<?php echo esc_url(get_permalink($chapter->ID)); ?>" class="chapter-link">
                                        <span class="chapter-number"><?php echo $display_title; ?></span>
                                        <span class="chapter-date"><?php echo get_the_date('M j, Y', $chapter->ID); ?></span>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else : ?>
                        <div class="no-chapters p-4 text-center">
                            <i class="bi bi-journal-x display-4 d-block mb-3 text-muted"></i>
                            No chapters available yet.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* General Styles */
body {
    background-color: #f8f9fa;
}

.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
}

/* Novel Cover */
.novel-cover img {
    width: 100%;
    height: auto;
}

/* Rating Section */
.rating-section {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.25rem;
}

.stars .fa-star {
    color: #ffc107;
    margin-right: 2px;
}

.stars-input .fa-star {
    cursor: pointer;
    color: #ffc107;
    margin-right: 2px;
}

.rating-value {
    margin-left: 0.5rem;
    font-weight: 500;
}

.rating-count {
    color: #6c757d;
    margin-left: 0.5rem;
}

/* Novel Info */
.novel-info .table td {
    padding: 0.5rem;
    border-color: #eee;
    font-size: 0.9rem;
}

.novel-info td:first-child {
    font-weight: 500;
    width: 45%;
    color: #495057;
}

.novel-info td:first-child i {
    margin-right: 0.5rem;
    color: #6c757d;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
}

/* Chapters Section */
.chapter-item {
    border-bottom: 1px solid #eee;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #212529;
    text-decoration: none;
    transition: background-color 0.2s;
}

.chapter-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.chapter-date {
    font-size: 0.875rem;
    color: #6c757d;
}

/* Synopsis */
.novel-synopsis {
    font-size: 0.95rem;
    line-height: 1.6;
}

.read-more {
    text-decoration: none;
    font-weight: 500;
}

/* Responsive */
@media (max-width: 768px) {
    .chapter-link {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .chapter-date {
        font-size: 0.8rem;
        margin-top: 0.25rem;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Read More functionality
    $('.read-more').click(function(e) {
        e.preventDefault();
        $('.synopsis-short').hide();
        $('.synopsis-full').fadeIn();
    });

    // Chapter sorting
    $('.chapter-controls select').change(function() {
        var order = $(this).val();
        var $chapterList = $('.chapter-list');
        var chapters = $chapterList.children('.chapter-item').get();
        
        chapters.sort(function(a, b) {
            var aNum = parseInt($(a).find('.chapter-number').text().match(/Chapter (\d+)/)[1]);
            var bNum = parseInt($(b).find('.chapter-number').text().match(/Chapter (\d+)/)[1]);
            return order === 'newest' ? bNum - aNum : aNum - bNum;
        });
        
        $.each(chapters, function(idx, item) { $chapterList.append(item); });
    });

    // Bookmark functionality
    $('.toggle-bookmark').on('click', function(e) {
        e.preventDefault();
        var button = $(this);
        var icon = button.find('i');
        var text = button.find('.bookmark-text');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'toggle_bookmark',
                novel_id: button.data('novel-id'),
                security: button.data('nonce')
            },
            beforeSend: function() {
                button.prop('disabled', true);
            },
            success: function(response) {
                if (response.success) {
                    if (response.data.action === 'added') {
                        icon.removeClass('far').addClass('fas');
                        text.text('Bookmarked');
                    } else {
                        icon.removeClass('fas').addClass('far');
                        text.text('Add Bookmark');
                    }
                }
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    });

    // Rating functionality
    $('.stars-input .fa-star').hover(
        function() {
            var rating = $(this).data('rating');
            for (var i = 1; i <= 5; i++) {
                if (i <= rating) {
                    $('.stars-input .fa-star:nth-child(' + i + ')').removeClass('far').addClass('fas');
                } else {
                    $('.stars-input .fa-star:nth-child(' + i + ')').removeClass('fas').addClass('far');
                }
            }
        },
        function() {
            $('.stars-input .fa-star').removeClass('fas').addClass('far');
        }
    );

    $('.stars-input .fa-star').click(function() {
        var rating = $(this).data('rating');
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: {
                action: 'save_novel_rating',
                post_id: <?php echo get_the_ID(); ?>,
                rating: rating
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.data);
                }
            }
        });
    });
});
</script>

<?php get_footer(); ?>
