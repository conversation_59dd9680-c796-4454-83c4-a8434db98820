<?php
/**
 * Debug Authentication Pages
 * This script checks the status of authentication pages and fixes common issues
 */

// Include WordPress
require_once('../../../wp-load.php');

// Must be admin to run this
if (!current_user_can('administrator')) {
    die('Access denied. Administrator privileges required.');
}

echo '<h1>Authentication Pages Debug Tool</h1>';
echo '<style>
    body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
    .success { color: #28a745; font-weight: bold; }
    .error { color: #dc3545; font-weight: bold; }
    .warning { color: #ffc107; font-weight: bold; }
    .info { color: #17a2b8; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }
    th { background-color: #f8f9fa; }
    .btn { padding: 8px 16px; margin: 4px; text-decoration: none; border-radius: 4px; display: inline-block; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-danger { background: #dc3545; color: white; }
</style>';

// Handle actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    if ($action === 'create_pages') {
        echo '<h2>Creating Missing Pages...</h2>';
        ln_reader_create_custom_pages();
        echo '<p class="success">✓ Page creation function executed</p>';
    }
    
    if ($action === 'flush_rules') {
        flush_rewrite_rules();
        echo '<p class="success">✓ Rewrite rules flushed</p>';
    }
    
    if ($action === 'enable_registration') {
        update_option('users_can_register', 1);
        update_option('default_role', 'subscriber');
        echo '<p class="success">✓ User registration enabled</p>';
    }
}

echo '<h2>1. WordPress Settings Check</h2>';
echo '<table>';
echo '<tr><th>Setting</th><th>Current Value</th><th>Required</th><th>Status</th></tr>';

$settings_check = array(
    'users_can_register' => array('current' => get_option('users_can_register'), 'required' => '1'),
    'default_role' => array('current' => get_option('default_role'), 'required' => 'subscriber'),
    'permalink_structure' => array('current' => get_option('permalink_structure'), 'required' => 'not empty')
);

foreach ($settings_check as $setting => $data) {
    $status = 'error';
    if ($setting === 'permalink_structure') {
        $status = !empty($data['current']) ? 'success' : 'error';
    } else {
        $status = ($data['current'] == $data['required']) ? 'success' : 'error';
    }
    
    echo "<tr>";
    echo "<td>{$setting}</td>";
    echo "<td>" . ($data['current'] ?: 'empty') . "</td>";
    echo "<td>{$data['required']}</td>";
    echo "<td class='{$status}'>" . ($status === 'success' ? '✓ OK' : '✗ ISSUE') . "</td>";
    echo "</tr>";
}
echo '</table>';

echo '<h2>2. Authentication Pages Status</h2>';
echo '<table>';
echo '<tr><th>Page</th><th>Status</th><th>ID</th><th>Template</th><th>URL</th><th>Template File Exists</th></tr>';

$pages_to_check = array('login', 'register', 'dashboard', 'reset-password');
$theme_dir = get_template_directory();

foreach ($pages_to_check as $slug) {
    $page = get_page_by_path($slug);
    $template_file = "page-{$slug}.php";
    $template_path = $theme_dir . '/' . $template_file;
    $template_exists = file_exists($template_path);
    
    if ($page) {
        $template = get_post_meta($page->ID, '_wp_page_template', true);
        $url = get_permalink($page->ID);
        $status = '<span class="success">EXISTS</span>';
    } else {
        $template = 'N/A';
        $url = 'N/A';
        $status = '<span class="error">MISSING</span>';
    }
    
    echo "<tr>";
    echo "<td>{$slug}</td>";
    echo "<td>{$status}</td>";
    echo "<td>" . ($page ? $page->ID : 'N/A') . "</td>";
    echo "<td>{$template}</td>";
    echo "<td>" . ($url !== 'N/A' ? "<a href='{$url}' target='_blank'>{$url}</a>" : 'N/A') . "</td>";
    echo "<td>" . ($template_exists ? '<span class="success">✓</span>' : '<span class="error">✗</span>') . "</td>";
    echo "</tr>";
}
echo '</table>';

echo '<h2>3. Authentication Functions Status</h2>';
$functions_check = array(
    'ln_reader_create_custom_pages' => function_exists('ln_reader_create_custom_pages'),
    'ln_reader_handle_login' => function_exists('ln_reader_handle_login'),
    'ln_reader_handle_registration' => function_exists('ln_reader_handle_registration'),
    'ln_reader_hide_admin_bar' => function_exists('ln_reader_hide_admin_bar'),
    'ln_reader_redirect_non_admin_users' => function_exists('ln_reader_redirect_non_admin_users')
);

echo '<table>';
echo '<tr><th>Function</th><th>Status</th></tr>';
foreach ($functions_check as $function => $exists) {
    $status = $exists ? '<span class="success">✓ EXISTS</span>' : '<span class="error">✗ MISSING</span>';
    echo "<tr><td>{$function}</td><td>{$status}</td></tr>";
}
echo '</table>';

echo '<h2>4. Quick Actions</h2>';
echo '<p>';
echo '<a href="?action=create_pages" class="btn btn-primary">Create Missing Pages</a>';
echo '<a href="?action=flush_rules" class="btn btn-success">Flush Rewrite Rules</a>';
echo '<a href="?action=enable_registration" class="btn btn-primary">Enable Registration</a>';
echo '</p>';

echo '<h2>5. Test URLs</h2>';
$test_urls = array(
    'Login' => home_url('/login'),
    'Register' => home_url('/register'),
    'Dashboard' => home_url('/dashboard'),
    'Reset Password' => home_url('/reset-password')
);

echo '<ul>';
foreach ($test_urls as $name => $url) {
    echo "<li><a href='{$url}' target='_blank'>{$name}: {$url}</a></li>";
}
echo '</ul>';

echo '<h2>6. Recent Error Log (if available)</h2>';
$error_log = WP_CONTENT_DIR . '/debug.log';
if (file_exists($error_log)) {
    $log_content = file_get_contents($error_log);
    $recent_lines = array_slice(explode("\n", $log_content), -20);
    echo '<pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">';
    echo esc_html(implode("\n", $recent_lines));
    echo '</pre>';
} else {
    echo '<p class="info">No debug.log file found. Enable WP_DEBUG_LOG to see errors.</p>';
}

echo '<hr>';
echo '<p><em>Debug completed at ' . date('Y-m-d H:i:s') . '</em></p>';
?>
